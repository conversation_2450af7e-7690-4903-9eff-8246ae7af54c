import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/room.dart';
import '../providers/component_provider.dart';
import '../providers/device_provider.dart';
import '../theme/app_theme.dart';
import '../screens/room_detail_screen.dart';

class RoomCard extends ConsumerWidget {
  final Room room;

  const RoomCard({
    super.key,
    required this.room,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final components = ref.watch(componentsByRoomProvider(room.id));
    final theme = Theme.of(context);
    
    // Check if all devices for this room's components are connected
    final isRoomOnline = _isRoomOnline(ref, components);
    
    return Card(
      elevation: room.isEnabled ? 2 : 1,
      child: InkWell(
        onTap: room.isEnabled && isRoomOnline
            ? () => _navigateToRoomDetail(context)
            : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getRoomColor(theme).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getRoomIcon(),
                      color: _getRoomColor(theme),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          room.name,
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: room.isEnabled && isRoomOnline
                                ? theme.colorScheme.onSurface
                                : theme.colorScheme.onSurface.withOpacity(0.5),
                          ),
                        ),
                        if (room.description != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            room.description!,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withOpacity(0.6),
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                  _buildStatusIndicator(theme, isRoomOnline),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Icon(
                    MdiIcons.devices,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${components.length} component${components.length != 1 ? 's' : ''}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                  const Spacer(),
                  if (!room.isEnabled)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.disabledColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'DISABLED',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: AppTheme.disabledColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    )
                  else if (!isRoomOnline)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.disconnectedColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'OFFLINE',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: AppTheme.disconnectedColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              if (components.isNotEmpty) ...[
                const SizedBox(height: 12),
                _buildComponentPreview(context, components),
              ],
            ],
          ),
        ),
      ),
    );
  }

  bool _isRoomOnline(WidgetRef ref, List<dynamic> components) {
    if (components.isEmpty) return true;
    
    final devices = ref.watch(deviceProvider);
    final deviceIds = components.map((c) => c.deviceId).toSet();
    
    for (final deviceId in deviceIds) {
      final device = devices.firstWhere(
        (d) => d.id == deviceId,
        orElse: () => throw StateError('Device not found'),
      );
      if (!device.isConnected || !device.isEnabled) {
        return false;
      }
    }
    return true;
  }

  Widget _buildStatusIndicator(ThemeData theme, bool isOnline) {
    if (!room.isEnabled) {
      return Icon(
        Icons.power_off,
        size: 20,
        color: AppTheme.disabledColor,
      );
    }
    
    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        color: isOnline ? AppTheme.connectedColor : AppTheme.disconnectedColor,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildComponentPreview(BuildContext context, List<dynamic> components) {
    final theme = Theme.of(context);
    final previewComponents = components.take(3).toList();
    
    return Row(
      children: [
        ...previewComponents.map((component) {
          return Container(
            margin: const EdgeInsets.only(right: 8),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppTheme.getComponentColor(component.type.name).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              component.type.displayName,
              style: theme.textTheme.labelSmall?.copyWith(
                color: AppTheme.getComponentColor(component.type.name),
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        }).toList(),
        if (components.length > 3)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '+${components.length - 3}',
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  IconData _getRoomIcon() {
    switch (room.iconName) {
      case 'home':
        return MdiIcons.home;
      case 'bedroom':
        return MdiIcons.bed;
      case 'living_room':
        return MdiIcons.sofa;
      case 'kitchen':
        return MdiIcons.chefHat;
      case 'bathroom':
        return MdiIcons.shower;
      case 'garage':
        return MdiIcons.garage;
      case 'garden':
        return MdiIcons.flower;
      case 'office':
        return MdiIcons.briefcase;
      case 'workshop':
        return MdiIcons.tools;
      default:
        return MdiIcons.home;
    }
  }

  Color _getRoomColor(ThemeData theme) {
    if (room.colorValue != null) {
      return Color(room.colorValue!);
    }
    return theme.colorScheme.primary;
  }

  void _navigateToRoomDetail(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RoomDetailScreen(room: room),
      ),
    );
  }
}
