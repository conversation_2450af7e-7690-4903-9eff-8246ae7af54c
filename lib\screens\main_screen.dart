import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'home_screen.dart';
import 'devices_screen.dart';
import 'automations_screen.dart';
import 'settings_screen.dart';

final currentTabProvider = StateProvider<int>((ref) => 0);

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTab = ref.watch(currentTabProvider);

    return Scaffold(
      body: IndexedStack(
        index: currentTab,
        children: const [
          HomeScreen(),
          DevicesScreen(),
          AutomationsScreen(),
          SettingsScreen(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: currentTab,
        onTap: (index) {
          ref.read(currentTabProvider.notifier).state = index;
        },
        items: [
          BottomNavigationBarItem(
            icon: Icon(MdiIcons.home),
            activeIcon: Icon(MdiIcons.home, color: Theme.of(context).primaryColor),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(MdiIcons.devices),
            activeIcon: Icon(MdiIcons.devices, color: Theme.of(context).primaryColor),
            label: 'Devices',
          ),
          BottomNavigationBarItem(
            icon: Icon(MdiIcons.autorenew),
            activeIcon: Icon(MdiIcons.autorenew, color: Theme.of(context).primaryColor),
            label: 'Automations',
          ),
          BottomNavigationBarItem(
            icon: Icon(MdiIcons.cog),
            activeIcon: Icon(MdiIcons.cog, color: Theme.of(context).primaryColor),
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}
