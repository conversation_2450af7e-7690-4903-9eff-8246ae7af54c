import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/automation.dart';
import '../models/component.dart';
import '../providers/automation_provider.dart';
import '../providers/component_provider.dart';
import '../providers/room_provider.dart';
import '../theme/app_theme.dart';

class AutomationCard extends ConsumerWidget {
  final Automation automation;

  const AutomationCard({
    super.key,
    required this.automation,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final room = ref.watch(roomByIdProvider(automation.roomId));
    final conditionComponent = ref.watch(componentByIdProvider(automation.condition.componentId));
    final actionComponent = ref.watch(componentByIdProvider(automation.action.componentId));

    return Card(
      elevation: automation.isEnabled ? 2 : 1,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: automation.isEnabled 
                        ? AppTheme.connectedColor.withOpacity(0.1)
                        : AppTheme.disabledColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    MdiIcons.autorenew,
                    color: automation.isEnabled 
                        ? AppTheme.connectedColor
                        : AppTheme.disabledColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        automation.name,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: automation.isEnabled
                              ? theme.colorScheme.onSurface
                              : theme.colorScheme.onSurface.withOpacity(0.5),
                        ),
                      ),
                      if (automation.description != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          automation.description!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                Switch(
                  value: automation.isEnabled,
                  onChanged: (value) {
                    ref.read(automationProvider.notifier)
                        .toggleAutomationEnabled(automation.id);
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Room info
            if (room != null) ...[
              Row(
                children: [
                  Icon(
                    MdiIcons.home,
                    size: 16,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    room.name,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
            ],

            // Automation logic
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  // IF condition
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'IF',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: Colors.blue,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _buildConditionText(conditionComponent),
                          style: theme.textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  
                  // THEN action
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'THEN',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _buildActionText(actionComponent),
                          style: theme.textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Execution stats
            Row(
              children: [
                Icon(
                  MdiIcons.counter,
                  size: 16,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 4),
                Text(
                  'Executed ${automation.executionCount} times',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
                const Spacer(),
                if (automation.lastExecuted != null) ...[
                  Text(
                    'Last: ${_formatDateTime(automation.lastExecuted!)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: automation.isEnabled 
                        ? () => _executeAutomation(ref)
                        : null,
                    icon: Icon(MdiIcons.play, size: 16),
                    label: const Text('Test Run'),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _showAutomationOptions(context, ref),
                  icon: Icon(MdiIcons.dotsVertical),
                  tooltip: 'Automation Options',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _buildConditionText(Component? component) {
    if (component == null) {
      return 'Unknown component ${automation.condition.comparator.symbol} ${automation.condition.value}';
    }
    
    return '${component.name} ${automation.condition.comparator.symbol} ${automation.condition.value}${component.unit ?? ''}';
  }

  String _buildActionText(Component? component) {
    if (component == null) {
      return 'Set unknown component to ${automation.action.value}';
    }
    
    return 'Set ${component.name} to ${automation.action.value}${component.unit ?? ''}';
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _executeAutomation(WidgetRef ref) {
    ref.read(automationProvider.notifier).executeAutomation(automation.id);
  }

  void _showAutomationOptions(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: Icon(MdiIcons.pencil),
            title: const Text('Edit Automation'),
            onTap: () {
              Navigator.of(context).pop();
              // Navigate to edit automation screen
            },
          ),
          ListTile(
            leading: Icon(MdiIcons.contentDuplicate),
            title: const Text('Duplicate Automation'),
            onTap: () {
              Navigator.of(context).pop();
              // Duplicate automation logic
            },
          ),
          ListTile(
            leading: Icon(MdiIcons.delete, color: Colors.red),
            title: const Text('Delete Automation'),
            onTap: () {
              Navigator.of(context).pop();
              _showDeleteConfirmation(context, ref);
            },
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Automation'),
        content: Text(
          'Are you sure you want to delete "${automation.name}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(automationProvider.notifier).deleteAutomation(automation.id);
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${automation.name} deleted')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
