import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import '../models/device.dart';
import '../providers/device_provider.dart';
import '../providers/app_settings_provider.dart';
import '../services/bluetooth_service.dart';

class AddDeviceScreen extends ConsumerStatefulWidget {
  const AddDeviceScreen({super.key});

  @override
  ConsumerState<AddDeviceScreen> createState() => _AddDeviceScreenState();
}

class _AddDeviceScreenState extends ConsumerState<AddDeviceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ipController = TextEditingController();
  final _portController = TextEditingController();
  
  DeviceType _selectedDeviceType = DeviceType.esp32Wifi;
  ConnectionType _selectedConnectionType = ConnectionType.wifi;
  String? _selectedBluetoothAddress;
  BluetoothDevice? _selectedBluetoothDevice;
  bool _isLoading = false;
  bool _isScanning = false;
  List<BluetoothDevice> _availableDevices = [];
  List<BluetoothDevice> _pairedDevices = [];
  final BluetoothService _bluetoothService = BluetoothService();

  @override
  void initState() {
    super.initState();
    // Set default WiFi settings
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final defaultSettings = ref.read(defaultWifiSettingsProvider);
      _ipController.text = defaultSettings.ip;
      _portController.text = defaultSettings.port.toString();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ipController.dispose();
    _portController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Device'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveDevice,
            child: _isLoading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Device Name',
                hintText: 'e.g., Living Room ESP32',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a device name';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            
            Text(
              'Device Type',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            
            _buildDeviceTypeSelector(),
            const SizedBox(height: 24),
            
            Text(
              'Connection Type',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            
            _buildConnectionTypeSelector(),
            const SizedBox(height: 24),
            
            if (_selectedConnectionType == ConnectionType.wifi) ...[
              Text(
                'WiFi Settings',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              
              TextFormField(
                controller: _ipController,
                decoration: const InputDecoration(
                  labelText: 'IP Address',
                  hintText: '***********',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter an IP address';
                  }
                  // Basic IP validation
                  final parts = value.split('.');
                  if (parts.length != 4) {
                    return 'Invalid IP address format';
                  }
                  for (final part in parts) {
                    final num = int.tryParse(part);
                    if (num == null || num < 0 || num > 255) {
                      return 'Invalid IP address';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _portController,
                decoration: const InputDecoration(
                  labelText: 'Port',
                  hintText: '80',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a port number';
                  }
                  final port = int.tryParse(value);
                  if (port == null || port < 1 || port > 65535) {
                    return 'Port must be between 1 and 65535';
                  }
                  return null;
                },
              ),
            ] else ...[
              Text(
                'Bluetooth Settings',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),

              _buildBluetoothSection(),
            ],
            
            const SizedBox(height: 32),
            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Device Information',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(_selectedDeviceType.description),
                    const SizedBox(height: 8),
                    Text('Max pins: ${_getMaxPins(_selectedDeviceType)}'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: "add_device_fab",
        onPressed: _isLoading ? null : _saveDevice,
        icon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
              )
            : const Icon(Icons.save),
        label: Text(_isLoading ? 'Saving...' : 'Save Device'),
        backgroundColor: _isLoading ? Colors.grey : null,
      ),
    );
  }

  Widget _buildDeviceTypeSelector() {
    return Column(
      children: DeviceType.values.map((type) {
        return RadioListTile<DeviceType>(
          title: Text(type.displayName),
          subtitle: Text(type.description),
          value: type,
          groupValue: _selectedDeviceType,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedDeviceType = value;
                // Auto-select appropriate connection type
                if (value == DeviceType.arduinoHC05 || value == DeviceType.esp32Bluetooth) {
                  _selectedConnectionType = ConnectionType.bluetooth;
                } else {
                  _selectedConnectionType = ConnectionType.wifi;
                }
              });
            }
          },
        );
      }).toList(),
    );
  }

  Widget _buildConnectionTypeSelector() {
    final availableTypes = _getAvailableConnectionTypes(_selectedDeviceType);
    
    return Column(
      children: availableTypes.map((type) {
        return RadioListTile<ConnectionType>(
          title: Text(type.displayName),
          value: type,
          groupValue: _selectedConnectionType,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedConnectionType = value;
              });
            }
          },
        );
      }).toList(),
    );
  }

  List<ConnectionType> _getAvailableConnectionTypes(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.arduinoHC05:
        return [ConnectionType.bluetooth];
      case DeviceType.esp8266Wifi:
        return [ConnectionType.wifi];
      case DeviceType.esp32Wifi:
        return [ConnectionType.wifi];
      case DeviceType.esp32Bluetooth:
        return [ConnectionType.bluetooth, ConnectionType.wifi];
      case DeviceType.arduinoUno:
      case DeviceType.arduinoNano:
      case DeviceType.arduinoMega:
        return [ConnectionType.bluetooth];
    }
  }

  int _getMaxPins(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.arduinoHC05:
        return 13;
      case DeviceType.esp8266Wifi:
        return 16;
      case DeviceType.esp32Wifi:
      case DeviceType.esp32Bluetooth:
        return 39;
      case DeviceType.arduinoUno:
      case DeviceType.arduinoNano:
        return 13;
      case DeviceType.arduinoMega:
        return 53;
    }
  }

  Widget _buildBluetoothSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Paired devices section
        if (_pairedDevices.isNotEmpty) ...[
          Text(
            'Paired Devices',
            style: Theme.of(context).textTheme.titleSmall,
          ),
          const SizedBox(height: 8),
          ...(_pairedDevices.map((device) => _buildDeviceCard(device, true))),
          const SizedBox(height: 16),
        ],

        // Scan for devices
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isScanning ? null : _startBluetoothScan,
                icon: _isScanning
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Icon(MdiIcons.bluetooth),
                label: Text(_isScanning ? 'Scanning...' : 'Scan for Devices'),
              ),
            ),
            if (_isScanning) ...[
              const SizedBox(width: 8),
              IconButton(
                onPressed: _stopBluetoothScan,
                icon: Icon(MdiIcons.stop),
                tooltip: 'Stop Scan',
              ),
            ],
          ],
        ),

        // Available devices
        if (_availableDevices.isNotEmpty) ...[
          const SizedBox(height: 16),
          Text(
            'Available Devices',
            style: Theme.of(context).textTheme.titleSmall,
          ),
          const SizedBox(height: 8),
          ...(_availableDevices.map((device) => _buildDeviceCard(device, false))),
        ],

        // Selected device info
        if (_selectedBluetoothDevice != null) ...[
          const SizedBox(height: 16),
          Card(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Icon(
                    MdiIcons.checkCircle,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Selected Device',
                          style: Theme.of(context).textTheme.labelMedium,
                        ),
                        Text(
                          _bluetoothService.getDeviceInfo(_selectedBluetoothDevice!),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDeviceCard(BluetoothDevice device, bool isPaired) {
    final isSelected = _selectedBluetoothDevice?.address == device.address;
    final isCompatible = _bluetoothService.isArduinoCompatible(device);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          isPaired ? MdiIcons.bluetoothConnect : MdiIcons.bluetooth,
          color: isCompatible
              ? Theme.of(context).primaryColor
              : Colors.grey,
        ),
        title: Text(device.name ?? 'Unknown Device'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(device.address),
            if (isCompatible)
              Text(
                'Arduino Compatible',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontSize: 12,
                ),
              ),
          ],
        ),
        trailing: isSelected
            ? Icon(
                MdiIcons.checkCircle,
                color: Theme.of(context).primaryColor,
              )
            : null,
        onTap: isCompatible ? () {
          setState(() {
            _selectedBluetoothDevice = device;
            _selectedBluetoothAddress = device.address;
          });
        } : null,
        enabled: isCompatible,
      ),
    );
  }

  Future<void> _startBluetoothScan() async {
    // Check permissions and Bluetooth state
    final hasPermissions = await _bluetoothService.checkAndRequestPermissions();
    if (!hasPermissions) {
      _showError('Bluetooth permissions are required');
      return;
    }

    final isAvailable = await _bluetoothService.isBluetoothAvailable();
    if (!isAvailable) {
      _showError('Bluetooth is not available on this device');
      return;
    }

    final isEnabled = await _bluetoothService.isBluetoothEnabled();
    if (!isEnabled) {
      final enabled = await _bluetoothService.requestBluetoothEnable();
      if (!enabled) {
        _showError('Bluetooth must be enabled to scan for devices');
        return;
      }
    }

    setState(() {
      _isScanning = true;
      _availableDevices.clear();
    });

    // Get paired devices
    final pairedDevices = await _bluetoothService.getPairedDevices();
    setState(() {
      _pairedDevices = pairedDevices;
    });

    // Start discovery
    _bluetoothService.startDiscovery().listen(
      (result) {
        if (!_availableDevices.any((d) => d.address == result.device.address)) {
          setState(() {
            _availableDevices.add(result.device);
          });
        }
      },
      onDone: () {
        setState(() {
          _isScanning = false;
        });
      },
      onError: (error) {
        setState(() {
          _isScanning = false;
        });
        _showError('Error during scan: $error');
      },
    );

    // Auto-stop after 30 seconds
    Future.delayed(const Duration(seconds: 30), () {
      if (_isScanning) {
        _stopBluetoothScan();
      }
    });
  }

  Future<void> _stopBluetoothScan() async {
    await _bluetoothService.stopDiscovery();
    setState(() {
      _isScanning = false;
    });
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _saveDevice() async {
    if (!_formKey.currentState!.validate()) return;

    // Validate Bluetooth selection
    if (_selectedConnectionType == ConnectionType.bluetooth && _selectedBluetoothDevice == null) {
      _showError('Please select a Bluetooth device');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(deviceProvider.notifier).addDevice(
        name: _nameController.text.trim(),
        type: _selectedDeviceType,
        connectionType: _selectedConnectionType,
        bluetoothAddress: _selectedConnectionType == ConnectionType.bluetooth
            ? _selectedBluetoothAddress
            : null,
        wifiIp: _selectedConnectionType == ConnectionType.wifi
            ? _ipController.text.trim()
            : null,
        wifiPort: _selectedConnectionType == ConnectionType.wifi
            ? int.parse(_portController.text.trim())
            : null,
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Device "${_nameController.text.trim()}" added'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
