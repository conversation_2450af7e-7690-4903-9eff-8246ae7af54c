import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/device.dart';
import '../providers/device_provider.dart';
import '../providers/app_settings_provider.dart';

class AddDeviceScreen extends ConsumerStatefulWidget {
  const AddDeviceScreen({super.key});

  @override
  ConsumerState<AddDeviceScreen> createState() => _AddDeviceScreenState();
}

class _AddDeviceScreenState extends ConsumerState<AddDeviceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ipController = TextEditingController();
  final _portController = TextEditingController();
  
  DeviceType _selectedDeviceType = DeviceType.esp32Wifi;
  ConnectionType _selectedConnectionType = ConnectionType.wifi;
  String? _selectedBluetoothAddress;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Set default WiFi settings
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final defaultSettings = ref.read(defaultWifiSettingsProvider);
      _ipController.text = defaultSettings.ip;
      _portController.text = defaultSettings.port.toString();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ipController.dispose();
    _portController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Device'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveDevice,
            child: _isLoading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Device Name',
                hintText: 'e.g., Living Room ESP32',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a device name';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            
            Text(
              'Device Type',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            
            _buildDeviceTypeSelector(),
            const SizedBox(height: 24),
            
            Text(
              'Connection Type',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            
            _buildConnectionTypeSelector(),
            const SizedBox(height: 24),
            
            if (_selectedConnectionType == ConnectionType.wifi) ...[
              Text(
                'WiFi Settings',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              
              TextFormField(
                controller: _ipController,
                decoration: const InputDecoration(
                  labelText: 'IP Address',
                  hintText: '***********',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter an IP address';
                  }
                  // Basic IP validation
                  final parts = value.split('.');
                  if (parts.length != 4) {
                    return 'Invalid IP address format';
                  }
                  for (final part in parts) {
                    final num = int.tryParse(part);
                    if (num == null || num < 0 || num > 255) {
                      return 'Invalid IP address';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _portController,
                decoration: const InputDecoration(
                  labelText: 'Port',
                  hintText: '80',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a port number';
                  }
                  final port = int.tryParse(value);
                  if (port == null || port < 1 || port > 65535) {
                    return 'Port must be between 1 and 65535';
                  }
                  return null;
                },
              ),
            ] else ...[
              Text(
                'Bluetooth Settings',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Icon(
                        MdiIcons.bluetooth,
                        size: 48,
                        color: Theme.of(context).primaryColor,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Bluetooth device discovery is not implemented in this demo.',
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'In a real app, this would scan for and pair with Bluetooth devices.',
                        style: TextStyle(fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
            
            const SizedBox(height: 32),
            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Device Information',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(_selectedDeviceType.description),
                    const SizedBox(height: 8),
                    Text('Max pins: ${_getMaxPins(_selectedDeviceType)}'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceTypeSelector() {
    return Column(
      children: DeviceType.values.map((type) {
        return RadioListTile<DeviceType>(
          title: Text(type.displayName),
          subtitle: Text(type.description),
          value: type,
          groupValue: _selectedDeviceType,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedDeviceType = value;
                // Auto-select appropriate connection type
                if (value == DeviceType.arduinoHC05 || value == DeviceType.esp32Bluetooth) {
                  _selectedConnectionType = ConnectionType.bluetooth;
                } else {
                  _selectedConnectionType = ConnectionType.wifi;
                }
              });
            }
          },
        );
      }).toList(),
    );
  }

  Widget _buildConnectionTypeSelector() {
    final availableTypes = _getAvailableConnectionTypes(_selectedDeviceType);
    
    return Column(
      children: availableTypes.map((type) {
        return RadioListTile<ConnectionType>(
          title: Text(type.displayName),
          value: type,
          groupValue: _selectedConnectionType,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedConnectionType = value;
              });
            }
          },
        );
      }).toList(),
    );
  }

  List<ConnectionType> _getAvailableConnectionTypes(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.arduinoHC05:
        return [ConnectionType.bluetooth];
      case DeviceType.esp8266Wifi:
        return [ConnectionType.wifi];
      case DeviceType.esp32Wifi:
        return [ConnectionType.wifi];
      case DeviceType.esp32Bluetooth:
        return [ConnectionType.bluetooth, ConnectionType.wifi];
    }
  }

  int _getMaxPins(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.arduinoHC05:
        return 13;
      case DeviceType.esp8266Wifi:
        return 16;
      case DeviceType.esp32Wifi:
      case DeviceType.esp32Bluetooth:
        return 39;
    }
  }

  Future<void> _saveDevice() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(deviceProvider.notifier).addDevice(
        name: _nameController.text.trim(),
        type: _selectedDeviceType,
        connectionType: _selectedConnectionType,
        bluetoothAddress: _selectedConnectionType == ConnectionType.bluetooth 
            ? 'DEMO:BLUETOOTH:ADDRESS' // Placeholder for demo
            : null,
        wifiIp: _selectedConnectionType == ConnectionType.wifi 
            ? _ipController.text.trim()
            : null,
        wifiPort: _selectedConnectionType == ConnectionType.wifi 
            ? int.parse(_portController.text.trim())
            : null,
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Device "${_nameController.text.trim()}" added'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
