// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_settings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AppSettingsAdapter extends TypeAdapter<AppSettings> {
  @override
  final int typeId = 10;

  @override
  AppSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AppSettings(
      themeMode: fields[0] as ThemeMode,
      defaultWifiIp: fields[1] as String,
      defaultWifiPort: fields[2] as int,
      notificationsEnabled: fields[3] as bool,
      autoConnectDevices: fields[4] as bool,
      connectionTimeout: fields[5] as int,
      keepScreenOn: fields[6] as bool,
      showDebugInfo: fields[7] as bool,
      createdAt: fields[8] as DateTime,
      updatedAt: fields[9] as DateTime,
      language: fields[10] as String,
      hapticFeedback: fields[11] as bool,
      refreshInterval: fields[12] as double,
    );
  }

  @override
  void write(BinaryWriter writer, AppSettings obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.themeMode)
      ..writeByte(1)
      ..write(obj.defaultWifiIp)
      ..writeByte(2)
      ..write(obj.defaultWifiPort)
      ..writeByte(3)
      ..write(obj.notificationsEnabled)
      ..writeByte(4)
      ..write(obj.autoConnectDevices)
      ..writeByte(5)
      ..write(obj.connectionTimeout)
      ..writeByte(6)
      ..write(obj.keepScreenOn)
      ..writeByte(7)
      ..write(obj.showDebugInfo)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.updatedAt)
      ..writeByte(10)
      ..write(obj.language)
      ..writeByte(11)
      ..write(obj.hapticFeedback)
      ..writeByte(12)
      ..write(obj.refreshInterval);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ThemeModeHiveAdapter extends TypeAdapter<ThemeModeHive> {
  @override
  final int typeId = 11;

  @override
  ThemeModeHive read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ThemeModeHive.system;
      case 1:
        return ThemeModeHive.light;
      case 2:
        return ThemeModeHive.dark;
      default:
        return ThemeModeHive.system;
    }
  }

  @override
  void write(BinaryWriter writer, ThemeModeHive obj) {
    switch (obj) {
      case ThemeModeHive.system:
        writer.writeByte(0);
        break;
      case ThemeModeHive.light:
        writer.writeByte(1);
        break;
      case ThemeModeHive.dark:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ThemeModeHiveAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppSettings _$AppSettingsFromJson(Map<String, dynamic> json) => AppSettings(
      themeMode: $enumDecodeNullable(_$ThemeModeEnumMap, json['themeMode']) ??
          ThemeMode.system,
      defaultWifiIp: json['defaultWifiIp'] as String? ?? '***********',
      defaultWifiPort: (json['defaultWifiPort'] as num?)?.toInt() ?? 80,
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
      autoConnectDevices: json['autoConnectDevices'] as bool? ?? true,
      connectionTimeout: (json['connectionTimeout'] as num?)?.toInt() ?? 10,
      keepScreenOn: json['keepScreenOn'] as bool? ?? false,
      showDebugInfo: json['showDebugInfo'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      language: json['language'] as String? ?? 'en',
      hapticFeedback: json['hapticFeedback'] as bool? ?? true,
      refreshInterval: (json['refreshInterval'] as num?)?.toDouble() ?? 2.0,
    );

Map<String, dynamic> _$AppSettingsToJson(AppSettings instance) =>
    <String, dynamic>{
      'themeMode': _$ThemeModeEnumMap[instance.themeMode]!,
      'defaultWifiIp': instance.defaultWifiIp,
      'defaultWifiPort': instance.defaultWifiPort,
      'notificationsEnabled': instance.notificationsEnabled,
      'autoConnectDevices': instance.autoConnectDevices,
      'connectionTimeout': instance.connectionTimeout,
      'keepScreenOn': instance.keepScreenOn,
      'showDebugInfo': instance.showDebugInfo,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'language': instance.language,
      'hapticFeedback': instance.hapticFeedback,
      'refreshInterval': instance.refreshInterval,
    };

const _$ThemeModeEnumMap = {
  ThemeMode.system: 'system',
  ThemeMode.light: 'light',
  ThemeMode.dark: 'dark',
};
