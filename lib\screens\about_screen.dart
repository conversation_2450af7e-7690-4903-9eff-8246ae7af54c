import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('About Arduverse'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // App Logo and Name
          Center(
            child: Column(
              children: [
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: Image.asset(
                      'assets/icons/app_icon.png',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Theme.of(context).primaryColor.withOpacity(0.1),
                          child: Icon(
                            Icons.android,
                            size: 40,
                            color: Theme.of(context).primaryColor,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Arduverse',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Professional IoT Controller',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Version 1.0.0',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
          
          // App Features
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Features',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildFeatureItem(
                    context,
                    MdiIcons.devices,
                    'Multi-Device Support',
                    'Control Arduino, ESP32, and ESP8266 devices',
                  ),
                  _buildFeatureItem(
                    context,
                    MdiIcons.wifi,
                    'WiFi & Bluetooth',
                    'Connect via WiFi or Bluetooth protocols',
                  ),
                  _buildFeatureItem(
                    context,
                    MdiIcons.home,
                    'Room Organization',
                    'Organize components into logical rooms',
                  ),
                  _buildFeatureItem(
                    context,
                    MdiIcons.autorenew,
                    'Smart Automations',
                    'Create IF-THEN automations for smart control',
                  ),
                  _buildFeatureItem(
                    context,
                    MdiIcons.chartLine,
                    'Real-time Monitoring',
                    'Live sensor data and device status',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Developer Info
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Developer',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundImage: const AssetImage('assets/images/profile.jpg'),
                        backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                        onBackgroundImageError: (exception, stackTrace) {
                          // Handle image loading error
                        },
                        child: const AssetImage('assets/images/profile.jpg') != null
                            ? null
                            : Icon(
                                Icons.person,
                                size: 30,
                                color: Theme.of(context).primaryColor,
                              ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'SK Raihan',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Electronics Engineer & Maker',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Founder of SKR Electronics Lab',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Social Links
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Connect with Us',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildSocialLink(
                    context,
                    MdiIcons.instagram,
                    'Instagram',
                    '@skr_electronics_lab',
                    'https://instagram.com/skr_electronics_lab',
                  ),
                  _buildSocialLink(
                    context,
                    MdiIcons.youtube,
                    'YouTube',
                    'SKR Electronics Lab',
                    'https://youtube.com/@skr_electronics_lab',
                  ),
                  _buildSocialLink(
                    context,
                    MdiIcons.twitter,
                    'X (Twitter)',
                    '@skrelectronics',
                    'https://twitter.com/skrelectronics',
                  ),
                  _buildSocialLink(
                    context,
                    MdiIcons.web,
                    'Website',
                    'SKR Electronics Lab',
                    'https://www.youtube.com/@skr_electronics_lab',
                  ),
                  _buildSocialLink(
                    context,
                    MdiIcons.coffee,
                    'Buy Me A Coffee',
                    'Support our work',
                    'https://buymeacoffee.com/skrelectronics',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Support
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Support',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    leading: Icon(MdiIcons.email),
                    title: const Text('Email Support'),
                    subtitle: const Text('<EMAIL>'),
                    onTap: () => _launchEmail(),
                  ),
                  ListTile(
                    leading: Icon(MdiIcons.fileDocument),
                    title: const Text('Documentation'),
                    subtitle: const Text('User guide and tutorials'),
                    onTap: () => _launchUrl('https://www.youtube.com/@skr_electronics_lab'),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Legal
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Legal',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    leading: Icon(MdiIcons.shield),
                    title: const Text('Privacy Policy'),
                    subtitle: const Text('How we handle your data'),
                    onTap: () => _showPrivacyPolicy(context),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 32),
          
          // Copyright
          Center(
            child: Text(
              '© 2025 SKR Electronics Lab\nAll rights reserved',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(
    BuildContext context,
    IconData icon,
    String title,
    String description,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            color: Theme.of(context).primaryColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSocialLink(
    BuildContext context,
    IconData icon,
    String platform,
    String handle,
    String url,
  ) {
    return ListTile(
      leading: Icon(icon),
      title: Text(platform),
      subtitle: Text(handle),
      onTap: () => _launchUrl(url),
    );
  }

  void _launchUrl(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  void _launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: 'Arduverse Support Request',
    );
    
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  void _showPrivacyPolicy(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Policy'),
        content: const SingleChildScrollView(
          child: Text(
            'Arduverse respects your privacy. This app:\n\n'
            '• Stores data locally on your device\n'
            '• Does not collect personal information\n'
            '• Does not share data with third parties\n'
            '• Uses device permissions only for app functionality\n\n'
            'For questions about privacy, contact <NAME_EMAIL>',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showLicenses(BuildContext context) {
    showLicensePage(context: context);
  }
}
