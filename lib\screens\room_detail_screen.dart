import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/room.dart';
import '../models/component.dart';
import '../providers/component_provider.dart';
import '../widgets/empty_state_widget.dart';

class RoomDetailScreen extends ConsumerWidget {
  final Room room;

  const RoomDetailScreen({
    super.key,
    required this.room,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final components = ref.watch(componentsByRoomProvider(room.id));

    return Scaffold(
      appBar: AppBar(
        title: Text(room.name),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.pencil),
            onPressed: () {
              // Navigate to edit room screen
            },
          ),
          IconButton(
            icon: Icon(MdiIcons.dotsVertical),
            onPressed: () => _showRoomOptions(context, ref),
          ),
        ],
      ),
      body: components.isEmpty
          ? EmptyStateWidget(
              icon: MdiIcons.devices,
              title: 'No Components Yet',
              message: 'Add components to this room to start controlling your devices',
              actionText: 'Add Component',
              onActionPressed: () {
                // Navigate to add component screen
              },
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: components.length,
              itemBuilder: (context, index) {
                final component = components[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: Icon(
                      _getComponentIcon(component.type.name),
                      color: Theme.of(context).primaryColor,
                    ),
                    title: Text(component.name),
                    subtitle: Text(component.type.displayName),
                    trailing: Text(component.displayValue),
                    onTap: () {
                      // Navigate to component detail screen
                    },
                  ),
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Navigate to add component screen
        },
        tooltip: 'Add Component',
        child: const Icon(Icons.add),
      ),
    );
  }

  IconData _getComponentIcon(String componentType) {
    switch (componentType) {
      case 'light':
        return MdiIcons.lightbulb;
      case 'relay':
        return MdiIcons.electricSwitch;
      case 'pwm':
        return MdiIcons.waveform;
      case 'rgb':
        return MdiIcons.ledOn;
      case 'pushButton':
        return MdiIcons.gesture;
      case 'digitalSensor':
        return MdiIcons.chip;
      case 'mic':
        return MdiIcons.microphone;
      case 'terminal':
        return MdiIcons.console;
      case 'dht11':
        return MdiIcons.thermometer;
      case 'ldr':
        return MdiIcons.brightness6;
      case 'pir':
        return MdiIcons.motionSensorOff;
      case 'ultrasonic':
        return MdiIcons.radar;
      default:
        return MdiIcons.chip;
    }
  }

  void _showRoomOptions(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: Icon(MdiIcons.pencil),
            title: const Text('Edit Room'),
            onTap: () {
              Navigator.of(context).pop();
              // Navigate to edit room screen
            },
          ),
          ListTile(
            leading: Icon(MdiIcons.plus),
            title: const Text('Add Component'),
            onTap: () {
              Navigator.of(context).pop();
              // Navigate to add component screen
            },
          ),
          ListTile(
            leading: Icon(MdiIcons.delete, color: Colors.red),
            title: const Text('Delete Room'),
            onTap: () {
              Navigator.of(context).pop();
              _showDeleteConfirmation(context, ref);
            },
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Room'),
        content: Text(
          'Are you sure you want to delete "${room.name}"? This will also remove all components in this room.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Delete room logic would go here
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to home screen
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${room.name} deleted')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
