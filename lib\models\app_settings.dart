import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';

part 'app_settings.g.dart';

@HiveType(typeId: 10)
@JsonSerializable()
class AppSettings {
  @HiveField(0)
  final ThemeMode themeMode;

  @HiveField(1)
  final String defaultWifiIp;

  @HiveField(2)
  final int defaultWifiPort;

  @HiveField(3)
  final bool notificationsEnabled;

  @HiveField(4)
  final bool autoConnectDevices;

  @HiveField(5)
  final int connectionTimeout;

  @HiveField(6)
  final bool keepScreenOn;

  @HiveField(7)
  final bool showDebugInfo;

  @HiveField(8)
  final DateTime createdAt;

  @HiveField(9)
  final DateTime updatedAt;

  @HiveField(10)
  final String language;

  @HiveField(11)
  final bool hapticFeedback;

  @HiveField(12)
  final double refreshInterval;

  const AppSettings({
    this.themeMode = ThemeMode.system,
    this.defaultWifiIp = '***********',
    this.defaultWifiPort = 80,
    this.notificationsEnabled = true,
    this.autoConnectDevices = true,
    this.connectionTimeout = 10,
    this.keepScreenOn = false,
    this.showDebugInfo = false,
    required this.createdAt,
    required this.updatedAt,
    this.language = 'en',
    this.hapticFeedback = true,
    this.refreshInterval = 2.0,
  });

  AppSettings copyWith({
    ThemeMode? themeMode,
    String? defaultWifiIp,
    int? defaultWifiPort,
    bool? notificationsEnabled,
    bool? autoConnectDevices,
    int? connectionTimeout,
    bool? keepScreenOn,
    bool? showDebugInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? language,
    bool? hapticFeedback,
    double? refreshInterval,
  }) {
    return AppSettings(
      themeMode: themeMode ?? this.themeMode,
      defaultWifiIp: defaultWifiIp ?? this.defaultWifiIp,
      defaultWifiPort: defaultWifiPort ?? this.defaultWifiPort,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      autoConnectDevices: autoConnectDevices ?? this.autoConnectDevices,
      connectionTimeout: connectionTimeout ?? this.connectionTimeout,
      keepScreenOn: keepScreenOn ?? this.keepScreenOn,
      showDebugInfo: showDebugInfo ?? this.showDebugInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      language: language ?? this.language,
      hapticFeedback: hapticFeedback ?? this.hapticFeedback,
      refreshInterval: refreshInterval ?? this.refreshInterval,
    );
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) => _$AppSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$AppSettingsToJson(this);

  factory AppSettings.defaultSettings() {
    final now = DateTime.now();
    return AppSettings(
      createdAt: now,
      updatedAt: now,
    );
  }

  bool isValidWifiIp(String ip) {
    final parts = ip.split('.');
    if (parts.length != 4) return false;
    
    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) return false;
    }
    return true;
  }

  bool isValidPort(int port) {
    return port > 0 && port <= 65535;
  }

  String get themeModeDisplayName {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppSettings &&
          runtimeType == other.runtimeType &&
          themeMode == other.themeMode &&
          defaultWifiIp == other.defaultWifiIp &&
          defaultWifiPort == other.defaultWifiPort;

  @override
  int get hashCode => Object.hash(themeMode, defaultWifiIp, defaultWifiPort);
}

// Hive adapter for ThemeMode
@HiveType(typeId: 11)
enum ThemeModeHive {
  @HiveField(0)
  system,
  @HiveField(1)
  light,
  @HiveField(2)
  dark,
}

extension ThemeModeExtension on ThemeMode {
  ThemeModeHive get toHive {
    switch (this) {
      case ThemeMode.system:
        return ThemeModeHive.system;
      case ThemeMode.light:
        return ThemeModeHive.light;
      case ThemeMode.dark:
        return ThemeModeHive.dark;
    }
  }
}

extension ThemeModeHiveExtension on ThemeModeHive {
  ThemeMode get toFlutter {
    switch (this) {
      case ThemeModeHive.system:
        return ThemeMode.system;
      case ThemeModeHive.light:
        return ThemeMode.light;
      case ThemeModeHive.dark:
        return ThemeMode.dark;
    }
  }
}
