import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'component.g.dart';

@HiveType(typeId: 3)
@JsonSerializable()
class Component {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final ComponentType type;

  @HiveField(3)
  final String deviceId;

  @HiveField(4)
  final int pin;

  @HiveField(5)
  final String iconName;

  @HiveField(6)
  final bool isActiveHigh;

  @HiveField(7)
  final String? unit;

  @HiveField(8)
  final String? customCommand;

  @HiveField(9)
  final double? currentValue;

  @HiveField(10)
  final bool isEnabled;

  @HiveField(11)
  final DateTime createdAt;

  @HiveField(12)
  final DateTime updatedAt;

  @HiveField(13)
  final Map<String, dynamic>? settings;

  @HiveField(14)
  final String? roomId;

  const Component({
    required this.id,
    required this.name,
    required this.type,
    required this.deviceId,
    required this.pin,
    required this.iconName,
    this.roomId,
    this.isActiveHigh = true,
    this.unit,
    this.customCommand,
    this.currentValue,
    this.isEnabled = true,
    required this.createdAt,
    required this.updatedAt,
    this.settings,
  });

  Component copyWith({
    String? id,
    String? name,
    ComponentType? type,
    String? deviceId,
    int? pin,
    String? iconName,
    String? roomId,
    bool? isActiveHigh,
    String? unit,
    String? customCommand,
    double? currentValue,
    bool? isEnabled,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? settings,
  }) {
    return Component(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      deviceId: deviceId ?? this.deviceId,
      pin: pin ?? this.pin,
      iconName: iconName ?? this.iconName,
      roomId: roomId ?? this.roomId,
      isActiveHigh: isActiveHigh ?? this.isActiveHigh,
      unit: unit ?? this.unit,
      customCommand: customCommand ?? this.customCommand,
      currentValue: currentValue ?? this.currentValue,
      isEnabled: isEnabled ?? this.isEnabled,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      settings: settings ?? this.settings,
    );
  }

  factory Component.fromJson(Map<String, dynamic> json) => _$ComponentFromJson(json);
  Map<String, dynamic> toJson() => _$ComponentToJson(this);

  String get displayValue {
    if (currentValue == null) return 'N/A';
    
    switch (type) {
      case ComponentType.light:
      case ComponentType.relay:
        return currentValue! > 0 ? 'ON' : 'OFF';
      case ComponentType.pwm:
        return '${currentValue!.toInt()}%';
      case ComponentType.pushButton:
        return currentValue! > 0 ? 'PRESSED' : 'RELEASED';
      case ComponentType.dht11:
        return '${currentValue!.toStringAsFixed(1)}${unit ?? '°C'}';
      case ComponentType.ldr:
      case ComponentType.mic:
        return '${currentValue!.toStringAsFixed(0)}${unit ?? ''}';
      case ComponentType.pir:
        return currentValue! > 0 ? 'MOTION' : 'NO MOTION';
      case ComponentType.ultrasonic:
        return '${currentValue!.toStringAsFixed(1)}${unit ?? 'cm'}';
      case ComponentType.rgb:
        return 'RGB: ${currentValue!.toInt()}';
      case ComponentType.digitalSensor:
        return currentValue! > 0 ? 'HIGH' : 'LOW';
      case ComponentType.terminal:
        return 'Terminal';
    }
  }

  bool get isOutput {
    return [
      ComponentType.light,
      ComponentType.relay,
      ComponentType.pwm,
      ComponentType.rgb,
      ComponentType.terminal,
    ].contains(type);
  }

  bool get isInput {
    return [
      ComponentType.pushButton,
      ComponentType.digitalSensor,
      ComponentType.dht11,
      ComponentType.ldr,
      ComponentType.mic,
      ComponentType.pir,
      ComponentType.ultrasonic,
    ].contains(type);
  }

  String getCommand(dynamic value) {
    if (customCommand != null && customCommand!.isNotEmpty) {
      return customCommand!.replaceAll('{value}', value.toString());
    }

    switch (type) {
      case ComponentType.light:
      case ComponentType.relay:
        final state = (value as bool) ? (isActiveHigh ? 1 : 0) : (isActiveHigh ? 0 : 1);
        return 'D$pin:$state';
      case ComponentType.pwm:
        return 'A$pin:${((value as double) * 255 / 100).toInt()}';
      case ComponentType.rgb:
        return 'RGB$pin:$value';
      default:
        return 'R$pin'; // Read command for sensors
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Component && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

@HiveType(typeId: 4)
enum ComponentType {
  @HiveField(0)
  light,
  @HiveField(1)
  relay,
  @HiveField(2)
  pwm,
  @HiveField(3)
  rgb,
  @HiveField(4)
  pushButton,
  @HiveField(5)
  digitalSensor,
  @HiveField(6)
  mic,
  @HiveField(7)
  terminal,
  @HiveField(8)
  dht11,
  @HiveField(9)
  ldr,
  @HiveField(10)
  pir,
  @HiveField(11)
  ultrasonic,
  @HiveField(12)
  fan,
  @HiveField(13)
  servo,
  @HiveField(14)
  motor,
  @HiveField(15)
  sensor,
  @HiveField(16)
  buzzer,
  @HiveField(17)
  led,
  @HiveField(18)
  custom,
}

extension ComponentTypeExtension on ComponentType {
  String get displayName {
    switch (this) {
      case ComponentType.light:
        return 'Light';
      case ComponentType.relay:
        return 'Relay';
      case ComponentType.pwm:
        return 'PWM';
      case ComponentType.rgb:
        return 'RGB LED (WS2812B)';
      case ComponentType.pushButton:
        return 'Push Button';
      case ComponentType.digitalSensor:
        return 'Digital Sensor';
      case ComponentType.mic:
        return 'Microphone';
      case ComponentType.terminal:
        return 'Terminal';
      case ComponentType.dht11:
        return 'DHT11 (Temp/Humidity)';
      case ComponentType.ldr:
        return 'LDR (Light Sensor)';
      case ComponentType.pir:
        return 'PIR Motion Sensor';
      case ComponentType.ultrasonic:
        return 'Ultrasonic Sensor';
      case ComponentType.fan:
        return 'Fan';
      case ComponentType.servo:
        return 'Servo Motor';
      case ComponentType.motor:
        return 'DC Motor';
      case ComponentType.sensor:
        return 'Sensor';
      case ComponentType.buzzer:
        return 'Buzzer';
      case ComponentType.led:
        return 'LED';
      case ComponentType.custom:
        return 'Custom Component';
    }
  }

  String get description {
    switch (this) {
      case ComponentType.light:
        return 'Control LED or light bulb';
      case ComponentType.relay:
        return 'Control high-power devices';
      case ComponentType.pwm:
        return 'Variable output control';
      case ComponentType.rgb:
        return 'Addressable RGB LED strip';
      case ComponentType.pushButton:
        return 'Digital input button';
      case ComponentType.digitalSensor:
        return 'Generic digital sensor';
      case ComponentType.mic:
        return 'Sound level detection';
      case ComponentType.terminal:
        return 'Send custom commands';
      case ComponentType.dht11:
        return 'Temperature and humidity sensor';
      case ComponentType.ldr:
        return 'Light intensity sensor';
      case ComponentType.pir:
        return 'Motion detection sensor';
      case ComponentType.ultrasonic:
        return 'Distance measurement sensor';
      case ComponentType.fan:
        return 'Variable speed fan control';
      case ComponentType.servo:
        return 'Precise angle control motor';
      case ComponentType.motor:
        return 'DC motor with speed control';
      case ComponentType.sensor:
        return 'Generic analog/digital sensor';
      case ComponentType.buzzer:
        return 'Sound alerts and notifications';
      case ComponentType.led:
        return 'Individual LED with brightness control';
      case ComponentType.custom:
        return 'User-defined custom component';
    }
  }

  List<String> get availableUnits {
    switch (this) {
      case ComponentType.dht11:
        return ['°C', '°F', '%'];
      case ComponentType.ldr:
        return ['lux', '%', 'raw'];
      case ComponentType.mic:
        return ['dB', '%', 'raw'];
      case ComponentType.ultrasonic:
        return ['cm', 'inch', 'mm'];
      case ComponentType.pwm:
        return ['%'];
      case ComponentType.fan:
        return ['%', 'RPM'];
      case ComponentType.servo:
        return ['°', 'degrees'];
      case ComponentType.motor:
        return ['%', 'RPM'];
      case ComponentType.sensor:
        return ['V', 'raw', '%'];
      case ComponentType.buzzer:
        return ['Hz', 'tone'];
      case ComponentType.led:
        return ['%', 'brightness'];
      default:
        return [];
    }
  }

  bool get requiresActiveHighSetting {
    return [
      ComponentType.light,
      ComponentType.relay,
      ComponentType.fan,
      ComponentType.motor,
      ComponentType.buzzer,
      ComponentType.led
    ].contains(this);
  }

  bool get supportsCustomCommand {
    return true; // All components support custom commands
  }
}
