import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'package:http/http.dart' as http;
import '../models/device.dart';

class DeviceConnectionService {
  static final DeviceConnectionService _instance = DeviceConnectionService._internal();
  factory DeviceConnectionService() => _instance;
  DeviceConnectionService._internal();

  final Map<String, BluetoothConnection?> _bluetoothConnections = {};
  final Map<String, Timer?> _connectionTimers = {};
  final Map<String, StreamSubscription?> _connectionSubscriptions = {};
  
  // Stream controller for connection status updates
  final StreamController<Map<String, bool>> _connectionStatusController = 
      StreamController<Map<String, bool>>.broadcast();
  
  Stream<Map<String, bool>> get connectionStatusStream => _connectionStatusController.stream;

  /// Connect to a device based on its type
  Future<bool> connectToDevice(Device device) async {
    try {
      switch (device.connectionType) {
        case ConnectionType.bluetooth:
          return await _connectBluetooth(device);
        case ConnectionType.wifi:
          return await _connectWiFi(device);
      }
    } catch (e) {
      print('Error connecting to device ${device.name}: $e');
      return false;
    }
  }

  /// Disconnect from a device
  Future<void> disconnectFromDevice(Device device) async {
    try {
      switch (device.connectionType) {
        case ConnectionType.bluetooth:
          await _disconnectBluetooth(device);
          break;
        case ConnectionType.wifi:
          await _disconnectWiFi(device);
          break;
      }
    } catch (e) {
      print('Error disconnecting from device ${device.name}: $e');
    }
  }

  /// Check if device is currently connected
  bool isDeviceConnected(Device device) {
    switch (device.connectionType) {
      case ConnectionType.bluetooth:
        return _bluetoothConnections[device.id]?.isConnected ?? false;
      case ConnectionType.wifi:
        // For WiFi, we'll implement a ping-based check
        return _connectionTimers[device.id]?.isActive ?? false;
    }
  }

  /// Send command to device
  Future<bool> sendCommand(Device device, String command) async {
    try {
      switch (device.connectionType) {
        case ConnectionType.bluetooth:
          return await _sendBluetoothCommand(device, command);
        case ConnectionType.wifi:
          return await _sendWiFiCommand(device, command);
      }
    } catch (e) {
      print('Error sending command to device ${device.name}: $e');
      return false;
    }
  }

  /// Start monitoring device connections
  void startConnectionMonitoring(List<Device> devices) {
    for (final device in devices) {
      _startDeviceMonitoring(device);
    }
  }

  /// Stop monitoring device connections
  void stopConnectionMonitoring() {
    for (final timer in _connectionTimers.values) {
      timer?.cancel();
    }
    for (final subscription in _connectionSubscriptions.values) {
      subscription?.cancel();
    }
    _connectionTimers.clear();
    _connectionSubscriptions.clear();
  }

  // Bluetooth connection methods
  Future<bool> _connectBluetooth(Device device) async {
    try {
      if (device.bluetoothAddress == null) return false;

      // Check if Bluetooth is enabled
      final isEnabled = await FlutterBluetoothSerial.instance.isEnabled;
      if (isEnabled != true) {
        print('Bluetooth is not enabled');
        return false;
      }

      // Parse Bluetooth address
      final address = device.bluetoothAddress!;
      final bluetoothDevice = BluetoothDevice.fromMap({
        'address': address,
        'name': device.name,
        'type': 1, // Classic Bluetooth
      });

      // Attempt connection
      final connection = await BluetoothConnection.toAddress(address);
      _bluetoothConnections[device.id] = connection;

      // Listen for disconnection
      _connectionSubscriptions[device.id] = connection.input?.listen(
        (data) {
          // Handle incoming data if needed
        },
        onDone: () {
          _handleDeviceDisconnected(device);
        },
        onError: (error) {
          print('Bluetooth connection error: $error');
          _handleDeviceDisconnected(device);
        },
      );

      _updateConnectionStatus(device.id, true);
      return true;
    } catch (e) {
      print('Bluetooth connection failed: $e');
      _updateConnectionStatus(device.id, false);
      return false;
    }
  }

  Future<void> _disconnectBluetooth(Device device) async {
    final connection = _bluetoothConnections[device.id];
    if (connection != null) {
      await connection.close();
      _bluetoothConnections.remove(device.id);
    }
    
    _connectionSubscriptions[device.id]?.cancel();
    _connectionSubscriptions.remove(device.id);
    
    _updateConnectionStatus(device.id, false);
  }

  Future<bool> _sendBluetoothCommand(Device device, String command) async {
    final connection = _bluetoothConnections[device.id];
    if (connection == null || !connection.isConnected) {
      return false;
    }

    try {
      connection.output.add(utf8.encode(command + '\n'));
      await connection.output.allSent;
      return true;
    } catch (e) {
      print('Failed to send Bluetooth command: $e');
      return false;
    }
  }

  // WiFi connection methods
  Future<bool> _connectWiFi(Device device) async {
    try {
      if (device.wifiIp == null || device.wifiPort == null) return false;

      final url = 'http://${device.wifiIp}:${device.wifiPort}/status';
      final response = await http.get(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        _updateConnectionStatus(device.id, true);
        _startWiFiHeartbeat(device);
        return true;
      } else {
        _updateConnectionStatus(device.id, false);
        return false;
      }
    } catch (e) {
      print('WiFi connection failed: $e');
      _updateConnectionStatus(device.id, false);
      return false;
    }
  }

  Future<void> _disconnectWiFi(Device device) async {
    _connectionTimers[device.id]?.cancel();
    _connectionTimers.remove(device.id);
    _updateConnectionStatus(device.id, false);
  }

  Future<bool> _sendWiFiCommand(Device device, String command) async {
    try {
      if (device.wifiIp == null || device.wifiPort == null) return false;

      final url = 'http://${device.wifiIp}:${device.wifiPort}/command';
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'command': command}),
      ).timeout(const Duration(seconds: 5));

      return response.statusCode == 200;
    } catch (e) {
      print('Failed to send WiFi command: $e');
      return false;
    }
  }

  void _startWiFiHeartbeat(Device device) {
    _connectionTimers[device.id] = Timer.periodic(
      const Duration(seconds: 10),
      (timer) async {
        final isConnected = await _checkWiFiConnection(device);
        if (!isConnected) {
          timer.cancel();
          _handleDeviceDisconnected(device);
        }
      },
    );
  }

  Future<bool> _checkWiFiConnection(Device device) async {
    try {
      if (device.wifiIp == null || device.wifiPort == null) return false;

      final url = 'http://${device.wifiIp}:${device.wifiPort}/ping';
      final response = await http.get(
        Uri.parse(url),
      ).timeout(const Duration(seconds: 3));

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  void _startDeviceMonitoring(Device device) {
    // Start periodic connection checks
    _connectionTimers[device.id] = Timer.periodic(
      const Duration(seconds: 15),
      (timer) async {
        if (device.isConnected) {
          bool actuallyConnected = false;
          
          switch (device.connectionType) {
            case ConnectionType.bluetooth:
              actuallyConnected = _bluetoothConnections[device.id]?.isConnected ?? false;
              break;
            case ConnectionType.wifi:
              actuallyConnected = await _checkWiFiConnection(device);
              break;
          }

          if (!actuallyConnected) {
            _handleDeviceDisconnected(device);
          }
        }
      },
    );
  }

  void _handleDeviceDisconnected(Device device) {
    _updateConnectionStatus(device.id, false);
    
    // Clean up connections
    switch (device.connectionType) {
      case ConnectionType.bluetooth:
        _bluetoothConnections.remove(device.id);
        _connectionSubscriptions[device.id]?.cancel();
        _connectionSubscriptions.remove(device.id);
        break;
      case ConnectionType.wifi:
        _connectionTimers[device.id]?.cancel();
        _connectionTimers.remove(device.id);
        break;
    }
  }

  void _updateConnectionStatus(String deviceId, bool isConnected) {
    final currentStatus = <String, bool>{deviceId: isConnected};
    _connectionStatusController.add(currentStatus);
  }

  void dispose() {
    stopConnectionMonitoring();
    _connectionStatusController.close();
    
    // Close all Bluetooth connections
    for (final connection in _bluetoothConnections.values) {
      connection?.close();
    }
    _bluetoothConnections.clear();
  }
}
