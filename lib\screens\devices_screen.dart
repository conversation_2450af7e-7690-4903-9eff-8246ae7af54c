import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/device_provider.dart';
import '../widgets/device_card.dart';
import '../widgets/empty_state_widget.dart';
import 'add_device_screen.dart';

class DevicesScreen extends ConsumerWidget {
  const DevicesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final devices = ref.watch(deviceProvider);
    final connectedDevices = ref.watch(connectedDevicesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Devices'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.refresh),
            onPressed: () {
              ref.invalidate(deviceProvider);
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(deviceProvider);
        },
        child: devices.isEmpty
            ? EmptyStateWidget(
                icon: MdiIcons.devices,
                title: 'No Devices Yet',
                message: 'Add your first Arduino, ESP32, or ESP8266 device to get started',
                actionText: 'Add Device',
                onActionPressed: () => _navigateToAddDevice(context),
              )
            : Column(
                children: [
                  if (devices.isNotEmpty) ...[
                    Container(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          _buildStatusChip(
                            context,
                            'Total',
                            devices.length.toString(),
                            Colors.blue,
                          ),
                          const SizedBox(width: 12),
                          _buildStatusChip(
                            context,
                            'Connected',
                            connectedDevices.length.toString(),
                            Colors.green,
                          ),
                          const SizedBox(width: 12),
                          _buildStatusChip(
                            context,
                            'Offline',
                            (devices.length - connectedDevices.length).toString(),
                            Colors.red,
                          ),
                        ],
                      ),
                    ),
                  ],
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: devices.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: DeviceCard(device: devices[index]),
                        );
                      },
                    ),
                  ),
                ],
              ),
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: "devices_fab",
        onPressed: () => _navigateToAddDevice(context),
        tooltip: 'Add Device',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatusChip(
    BuildContext context,
    String label,
    String value,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToAddDevice(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddDeviceScreen(),
      ),
    );
  }
}
