import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/room.dart';
import '../services/storage_service.dart';

class RoomNotifier extends StateNotifier<List<Room>> {
  RoomNotifier(this._storageService) : super([]) {
    _loadRooms();
  }

  final StorageService _storageService;
  static const _uuid = Uuid();

  Future<void> _loadRooms() async {
    try {
      final rooms = await _storageService.getRooms();
      state = rooms;
    } catch (e) {
      state = [];
    }
  }

  Future<void> addRoom({
    required String name,
    required String iconName,
    String? description,
    int? colorValue,
    List<String> componentIds = const [],
  }) async {
    if (name.trim().isEmpty) {
      throw ArgumentError('Room name cannot be empty');
    }

    if (state.any((room) => room.name.toLowerCase() == name.toLowerCase())) {
      throw ArgumentError('A room with this name already exists');
    }

    final now = DateTime.now();
    final newRoom = Room(
      id: _uuid.v4(),
      name: name.trim(),
      iconName: iconName,
      componentIds: componentIds,
      description: description,
      colorValue: colorValue,
      createdAt: now,
      updatedAt: now,
    );

    state = [...state, newRoom];
    await _storageService.saveRoom(newRoom);
  }

  Future<void> updateRoom(Room updatedRoom) async {
    final index = state.indexWhere((room) => room.id == updatedRoom.id);
    if (index == -1) {
      throw ArgumentError('Room not found');
    }

    final newState = [...state];
    newState[index] = updatedRoom.copyWith(updatedAt: DateTime.now());
    state = newState;
    
    await _storageService.saveRoom(newState[index]);
  }

  Future<void> deleteRoom(String roomId) async {
    final room = state.firstWhere(
      (room) => room.id == roomId,
      orElse: () => throw ArgumentError('Room not found'),
    );

    state = state.where((room) => room.id != roomId).toList();
    await _storageService.deleteRoom(roomId);
  }

  Future<void> toggleRoomEnabled(String roomId) async {
    final room = getRoomById(roomId);
    if (room == null) return;

    await updateRoom(room.copyWith(isEnabled: !room.isEnabled));
  }

  Future<void> addComponentToRoom(String roomId, String componentId) async {
    final room = getRoomById(roomId);
    if (room == null) return;

    final updatedRoom = room.addComponent(componentId);
    await updateRoom(updatedRoom);
  }

  Future<void> removeComponentFromRoom(String roomId, String componentId) async {
    final room = getRoomById(roomId);
    if (room == null) return;

    final updatedRoom = room.removeComponent(componentId);
    await updateRoom(updatedRoom);
  }

  Room? getRoomById(String roomId) {
    try {
      return state.firstWhere((room) => room.id == roomId);
    } catch (e) {
      return null;
    }
  }

  List<Room> getEnabledRooms() {
    return state.where((room) => room.isEnabled).toList();
  }

  List<Room> getRoomsWithComponents() {
    return state.where((room) => room.hasComponents).toList();
  }
}

final roomProvider = StateNotifierProvider<RoomNotifier, List<Room>>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return RoomNotifier(storageService);
});

// Convenience providers
final enabledRoomsProvider = Provider<List<Room>>((ref) {
  final rooms = ref.watch(roomProvider);
  return rooms.where((room) => room.isEnabled).toList();
});

final roomsWithComponentsProvider = Provider<List<Room>>((ref) {
  final rooms = ref.watch(roomProvider);
  return rooms.where((room) => room.hasComponents).toList();
});

// Provider for getting a specific room by ID
final roomByIdProvider = Provider.family<Room?, String>((ref, roomId) {
  final rooms = ref.watch(roomProvider);
  try {
    return rooms.firstWhere((room) => room.id == roomId);
  } catch (e) {
    return null;
  }
});

// Provider for checking if any rooms exist
final hasRoomsProvider = Provider<bool>((ref) {
  final rooms = ref.watch(roomProvider);
  return rooms.isNotEmpty;
});
