import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'package:permission_handler/permission_handler.dart';

class BluetoothService {
  static final BluetoothService _instance = BluetoothService._internal();
  factory BluetoothService() => _instance;
  BluetoothService._internal();

  FlutterBluetoothSerial get _bluetooth => FlutterBluetoothSerial.instance;

  // Check if Bluetooth is available and enabled
  Future<bool> isBluetoothAvailable() async {
    try {
      return await _bluetooth.isAvailable ?? false;
    } catch (e) {
      debugPrint('Error checking Bluetooth availability: $e');
      return false;
    }
  }

  // Check if Bluetooth is enabled
  Future<bool> isBluetoothEnabled() async {
    try {
      return await _bluetooth.isEnabled ?? false;
    } catch (e) {
      debugPrint('Error checking Bluetooth state: $e');
      return false;
    }
  }

  // Request to enable Bluetooth
  Future<bool> requestBluetoothEnable() async {
    try {
      return await _bluetooth.requestEnable() ?? false;
    } catch (e) {
      debugPrint('Error requesting Bluetooth enable: $e');
      return false;
    }
  }

  // Check and request permissions
  Future<bool> checkAndRequestPermissions() async {
    try {
      // Check location permission (required for Bluetooth scanning)
      var locationStatus = await Permission.location.status;
      if (!locationStatus.isGranted) {
        locationStatus = await Permission.location.request();
        if (!locationStatus.isGranted) {
          return false;
        }
      }

      // Check Bluetooth permissions
      var bluetoothStatus = await Permission.bluetooth.status;
      if (!bluetoothStatus.isGranted) {
        bluetoothStatus = await Permission.bluetooth.request();
        if (!bluetoothStatus.isGranted) {
          return false;
        }
      }

      // Check Bluetooth scan permission (Android 12+)
      var bluetoothScanStatus = await Permission.bluetoothScan.status;
      if (!bluetoothScanStatus.isGranted) {
        bluetoothScanStatus = await Permission.bluetoothScan.request();
        if (!bluetoothScanStatus.isGranted) {
          return false;
        }
      }

      // Check Bluetooth connect permission (Android 12+)
      var bluetoothConnectStatus = await Permission.bluetoothConnect.status;
      if (!bluetoothConnectStatus.isGranted) {
        bluetoothConnectStatus = await Permission.bluetoothConnect.request();
        if (!bluetoothConnectStatus.isGranted) {
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('Error checking permissions: $e');
      return false;
    }
  }

  // Get paired devices
  Future<List<BluetoothDevice>> getPairedDevices() async {
    try {
      return await _bluetooth.getBondedDevices();
    } catch (e) {
      debugPrint('Error getting paired devices: $e');
      return [];
    }
  }

  // Start device discovery
  Stream<BluetoothDiscoveryResult> startDiscovery() {
    try {
      return _bluetooth.startDiscovery();
    } catch (e) {
      debugPrint('Error starting discovery: $e');
      return const Stream.empty();
    }
  }

  // Stop device discovery
  Future<void> stopDiscovery() async {
    try {
      await _bluetooth.cancelDiscovery();
    } catch (e) {
      debugPrint('Error stopping discovery: $e');
    }
  }

  // Connect to a device
  Future<BluetoothConnection?> connectToDevice(BluetoothDevice device) async {
    try {
      return await BluetoothConnection.toAddress(device.address);
    } catch (e) {
      debugPrint('Error connecting to device ${device.name}: $e');
      return null;
    }
  }

  // Send data to connected device
  Future<bool> sendData(BluetoothConnection connection, String data) async {
    try {
      connection.output.add(Uint8List.fromList(data.codeUnits));
      await connection.output.allSent;
      return true;
    } catch (e) {
      debugPrint('Error sending data: $e');
      return false;
    }
  }

  // Listen for incoming data
  Stream<String> listenForData(BluetoothConnection connection) {
    try {
      return connection.input!.map((data) => String.fromCharCodes(data));
    } catch (e) {
      debugPrint('Error listening for data: $e');
      return const Stream.empty();
    }
  }

  // Disconnect from device
  Future<void> disconnect(BluetoothConnection connection) async {
    try {
      await connection.close();
    } catch (e) {
      debugPrint('Error disconnecting: $e');
    }
  }

  // Check if device is connected
  bool isConnected(BluetoothConnection? connection) {
    return connection?.isConnected ?? false;
  }

  // Get device info string
  String getDeviceInfo(BluetoothDevice device) {
    return '${device.name ?? 'Unknown'} (${device.address})';
  }

  // Validate device for Arduino/ESP compatibility
  bool isArduinoCompatible(BluetoothDevice device) {
    final name = device.name?.toLowerCase() ?? '';
    return name.contains('hc-05') || 
           name.contains('hc-06') || 
           name.contains('esp32') || 
           name.contains('arduino') ||
           name.contains('bt-');
  }
}
