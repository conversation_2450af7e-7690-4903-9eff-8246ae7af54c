import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/automation.dart';
import '../providers/automation_provider.dart';
import '../providers/room_provider.dart';
import '../providers/component_provider.dart';

class AddAutomationScreen extends ConsumerStatefulWidget {
  const AddAutomationScreen({super.key});

  @override
  ConsumerState<AddAutomationScreen> createState() => _AddAutomationScreenState();
}

class _AddAutomationScreenState extends ConsumerState<AddAutomationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _valueController = TextEditingController();
  final _actionValueController = TextEditingController();
  
  String? _selectedRoomId;
  String? _conditionComponentId;
  String? _actionComponentId;
  Comparator _selectedComparator = Comparator.greaterThan;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _valueController.dispose();
    _actionValueController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final rooms = ref.watch(roomProvider);
    final components = ref.watch(componentProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Automation'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveAutomation,
            child: _isLoading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Automation Name',
                hintText: 'e.g., Turn on lights when motion detected',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter an automation name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                hintText: 'Brief description of what this automation does',
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 24),
            
            // Room Selection
            Text(
              'Select Room',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            
            DropdownButtonFormField<String>(
              value: _selectedRoomId,
              decoration: const InputDecoration(
                labelText: 'Room',
                hintText: 'Choose a room',
              ),
              items: rooms.map((room) {
                return DropdownMenuItem(
                  value: room.id,
                  child: Text(room.name),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedRoomId = value;
                  // Reset component selections when room changes
                  _conditionComponentId = null;
                  _actionComponentId = null;
                });
              },
              validator: (value) {
                if (value == null) {
                  return 'Please select a room';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            
            // IF Condition
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'IF',
                            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Condition',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    DropdownButtonFormField<String>(
                      value: _conditionComponentId,
                      decoration: const InputDecoration(
                        labelText: 'Input Component',
                        hintText: 'Choose a sensor or input',
                      ),
                      items: components
                          .where((c) => c.isInput)
                          .map((component) {
                        return DropdownMenuItem(
                          value: component.id,
                          child: Text('${component.name} (${component.type.displayName})'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _conditionComponentId = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Please select an input component';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<Comparator>(
                            value: _selectedComparator,
                            decoration: const InputDecoration(
                              labelText: 'Comparator',
                            ),
                            items: Comparator.values.map((comparator) {
                              return DropdownMenuItem(
                                value: comparator,
                                child: Text('${comparator.displayName} (${comparator.symbol})'),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _selectedComparator = value;
                                });
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _valueController,
                            decoration: const InputDecoration(
                              labelText: 'Value',
                              hintText: 'e.g., 25, 1, 0',
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter a value';
                              }
                              if (double.tryParse(value) == null) {
                                return 'Please enter a valid number';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // THEN Action
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'THEN',
                            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Action',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    DropdownButtonFormField<String>(
                      value: _actionComponentId,
                      decoration: const InputDecoration(
                        labelText: 'Output Component',
                        hintText: 'Choose a device to control',
                      ),
                      items: components
                          .where((c) => c.isOutput)
                          .map((component) {
                        return DropdownMenuItem(
                          value: component.id,
                          child: Text('${component.name} (${component.type.displayName})'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _actionComponentId = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Please select an output component';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _actionValueController,
                      decoration: const InputDecoration(
                        labelText: 'Action Value',
                        hintText: 'e.g., 1 (on), 0 (off), 75 (percentage)',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter an action value';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 32),
            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'How Automations Work',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '• Automations run automatically when the condition is met\n'
                      '• Input components are sensors that provide data\n'
                      '• Output components are devices that can be controlled\n'
                      '• You can enable/disable automations anytime',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveAutomation() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final condition = AutomationCondition(
        componentId: _conditionComponentId!,
        comparator: _selectedComparator,
        value: double.parse(_valueController.text.trim()),
      );

      final action = AutomationAction(
        componentId: _actionComponentId!,
        value: _actionValueController.text.trim(),
      );

      await ref.read(automationProvider.notifier).addAutomation(
        name: _nameController.text.trim(),
        roomId: _selectedRoomId!,
        condition: condition,
        action: action,
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Automation "${_nameController.text.trim()}" created'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
