import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'automation.g.dart';

@HiveType(typeId: 6)
@JsonSerializable()
class Automation {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String roomId;

  @HiveField(3)
  final AutomationCondition condition;

  @HiveField(4)
  final AutomationAction action;

  @HiveField(5)
  final bool isEnabled;

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  final DateTime updatedAt;

  @HiveField(8)
  final String? description;

  @HiveField(9)
  final int executionCount;

  @HiveField(10)
  final DateTime? lastExecuted;

  const Automation({
    required this.id,
    required this.name,
    required this.roomId,
    required this.condition,
    required this.action,
    this.isEnabled = true,
    required this.createdAt,
    required this.updatedAt,
    this.description,
    this.executionCount = 0,
    this.lastExecuted,
  });

  Automation copyWith({
    String? id,
    String? name,
    String? roomId,
    AutomationCondition? condition,
    AutomationAction? action,
    bool? isEnabled,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? description,
    int? executionCount,
    DateTime? lastExecuted,
  }) {
    return Automation(
      id: id ?? this.id,
      name: name ?? this.name,
      roomId: roomId ?? this.roomId,
      condition: condition ?? this.condition,
      action: action ?? this.action,
      isEnabled: isEnabled ?? this.isEnabled,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      description: description ?? this.description,
      executionCount: executionCount ?? this.executionCount,
      lastExecuted: lastExecuted ?? this.lastExecuted,
    );
  }

  factory Automation.fromJson(Map<String, dynamic> json) => _$AutomationFromJson(json);
  Map<String, dynamic> toJson() => _$AutomationToJson(this);

  bool evaluateCondition(double currentValue) {
    switch (condition.comparator) {
      case Comparator.equals:
        return currentValue == condition.value;
      case Comparator.greaterThan:
        return currentValue > condition.value;
      case Comparator.lessThan:
        return currentValue < condition.value;
      case Comparator.greaterThanOrEqual:
        return currentValue >= condition.value;
      case Comparator.lessThanOrEqual:
        return currentValue <= condition.value;
    }
  }

  Automation incrementExecutionCount() {
    return copyWith(
      executionCount: executionCount + 1,
      lastExecuted: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  String get conditionDescription {
    return '${condition.componentId} ${condition.comparator.symbol} ${condition.value}';
  }

  String get actionDescription {
    return '${action.componentId} → ${action.value}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Automation && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

@HiveType(typeId: 7)
@JsonSerializable()
class AutomationCondition {
  @HiveField(0)
  final String componentId;

  @HiveField(1)
  final Comparator comparator;

  @HiveField(2)
  final double value;

  const AutomationCondition({
    required this.componentId,
    required this.comparator,
    required this.value,
  });

  factory AutomationCondition.fromJson(Map<String, dynamic> json) => 
      _$AutomationConditionFromJson(json);
  Map<String, dynamic> toJson() => _$AutomationConditionToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AutomationCondition &&
          runtimeType == other.runtimeType &&
          componentId == other.componentId &&
          comparator == other.comparator &&
          value == other.value;

  @override
  int get hashCode => Object.hash(componentId, comparator, value);
}

@HiveType(typeId: 8)
@JsonSerializable()
class AutomationAction {
  @HiveField(0)
  final String componentId;

  @HiveField(1)
  final dynamic value;

  const AutomationAction({
    required this.componentId,
    required this.value,
  });

  factory AutomationAction.fromJson(Map<String, dynamic> json) => 
      _$AutomationActionFromJson(json);
  Map<String, dynamic> toJson() => _$AutomationActionToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AutomationAction &&
          runtimeType == other.runtimeType &&
          componentId == other.componentId &&
          value == other.value;

  @override
  int get hashCode => Object.hash(componentId, value);
}

@HiveType(typeId: 9)
enum Comparator {
  @HiveField(0)
  equals,
  @HiveField(1)
  greaterThan,
  @HiveField(2)
  lessThan,
  @HiveField(3)
  greaterThanOrEqual,
  @HiveField(4)
  lessThanOrEqual,
}

extension ComparatorExtension on Comparator {
  String get displayName {
    switch (this) {
      case Comparator.equals:
        return 'Equals';
      case Comparator.greaterThan:
        return 'Greater Than';
      case Comparator.lessThan:
        return 'Less Than';
      case Comparator.greaterThanOrEqual:
        return 'Greater Than or Equal';
      case Comparator.lessThanOrEqual:
        return 'Less Than or Equal';
    }
  }

  String get symbol {
    switch (this) {
      case Comparator.equals:
        return '=';
      case Comparator.greaterThan:
        return '>';
      case Comparator.lessThan:
        return '<';
      case Comparator.greaterThanOrEqual:
        return '>=';
      case Comparator.lessThanOrEqual:
        return '<=';
    }
  }
}
