import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/component.dart';
import '../models/device.dart';
import '../providers/component_provider.dart';
import '../providers/device_provider.dart';


class AddComponentScreen extends ConsumerStatefulWidget {
  final String? roomId;
  final String? deviceId;

  const AddComponentScreen({
    super.key,
    this.roomId,
    this.deviceId,
  });

  @override
  ConsumerState<AddComponentScreen> createState() => _AddComponentScreenState();
}

class _AddComponentScreenState extends ConsumerState<AddComponentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _customCommandController = TextEditingController();
  
  ComponentType _selectedComponentType = ComponentType.light;
  String? _selectedDeviceId;
  int? _selectedPin;
  String _selectedIconName = 'lightbulb';
  bool _isActiveHigh = true;
  String? _selectedUnit;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedDeviceId = widget.deviceId;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _customCommandController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final devices = ref.watch(enabledDevicesProvider);
    final selectedDevice = _selectedDeviceId != null 
        ? ref.watch(deviceByIdProvider(_selectedDeviceId!))
        : null;
    final usedPins = _selectedDeviceId != null 
        ? ref.watch(usedPinsProvider(_selectedDeviceId!))
        : <int>[];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Component'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveComponent,
            child: _isLoading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: devices.isEmpty
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.devices_other, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text('No devices available'),
                  SizedBox(height: 8),
                  Text('Add a device first to create components'),
                ],
              ),
            )
          : Form(
              key: _formKey,
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // Component Name
                  TextFormField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'Component Name',
                      hintText: 'e.g., Living Room Light, Temperature Sensor',
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter a component name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  
                  // Device Selection
                  Text(
                    'Select Device',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  
                  DropdownButtonFormField<String>(
                    value: _selectedDeviceId,
                    decoration: const InputDecoration(
                      labelText: 'Device',
                      hintText: 'Choose a device',
                    ),
                    items: devices.map((device) {
                      return DropdownMenuItem(
                        value: device.id,
                        child: Row(
                          children: [
                            Icon(
                              _getDeviceIcon(device.type),
                              size: 20,
                              color: device.isConnected ? Colors.green : Colors.grey,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text('${device.name} (${device.type.displayName})'),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedDeviceId = value;
                        _selectedPin = null; // Reset pin selection
                      });
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'Please select a device';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  
                  // Component Type Selection
                  Text(
                    'Component Type',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  
                  _buildComponentTypeGrid(),
                  const SizedBox(height: 24),
                  
                  // Pin Selection
                  if (selectedDevice != null) ...[
                    Text(
                      'Pin Selection',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    
                    _buildPinSelector(selectedDevice, usedPins),
                    const SizedBox(height: 24),
                  ],
                  
                  // Component Settings
                  _buildComponentSettings(),
                  
                  const SizedBox(height: 32),
                  
                  // Info Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Component Info',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text(_selectedComponentType.description),
                          if (_selectedComponentType.requiresActiveHighSetting) ...[
                            const SizedBox(height: 8),
                            const Text('• Configure active high/low setting for proper operation'),
                          ],
                          if (_selectedComponentType.availableUnits.isNotEmpty) ...[
                            const SizedBox(height: 8),
                            Text('• Available units: ${_selectedComponentType.availableUnits.join(', ')}'),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildComponentTypeGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 2.5,
      ),
      itemCount: ComponentType.values.length,
      itemBuilder: (context, index) {
        final type = ComponentType.values[index];
        final isSelected = type == _selectedComponentType;
        
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedComponentType = type;
              _selectedPin = null; // Reset pin when type changes
              
              // Set default icon based on type
              _selectedIconName = _getDefaultIcon(type);
              
              // Set default unit if available
              if (type.availableUnits.isNotEmpty) {
                _selectedUnit = type.availableUnits.first;
              } else {
                _selectedUnit = null;
              }
            });
          },
          child: Container(
            decoration: BoxDecoration(
              color: isSelected 
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : null,
              border: Border.all(
                color: isSelected 
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).dividerColor,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.all(8),
            child: Row(
              children: [
                Icon(
                  _getComponentIcon(type),
                  color: isSelected 
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).iconTheme.color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    type.displayName,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isSelected 
                          ? Theme.of(context).primaryColor
                          : null,
                      fontWeight: isSelected ? FontWeight.w500 : null,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPinSelector(Device device, List<int> usedPins) {
    final availablePins = <int>[];
    for (int i = 0; i <= device.getMaxPins(); i++) {
      if (!usedPins.contains(i)) {
        availablePins.add(i);
      }
    }

    if (availablePins.isEmpty) {
      return Card(
        color: Colors.red.withOpacity(0.1),
        child: const Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(Icons.warning, color: Colors.red),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'No available pins on this device. All pins are already in use.',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Pins (${availablePins.length}/${device.getMaxPins() + 1})',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: availablePins.map((pin) {
            final isSelected = pin == _selectedPin;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedPin = pin;
                });
              },
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).cardColor,
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).dividerColor,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    pin.toString(),
                    style: TextStyle(
                      color: isSelected
                          ? Colors.white
                          : Theme.of(context).textTheme.bodyMedium?.color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        if (usedPins.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            'Used pins: ${usedPins.join(', ')}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildComponentSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Component Settings',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),

        // Active High/Low Setting
        if (_selectedComponentType.requiresActiveHighSetting) ...[
          SwitchListTile(
            title: const Text('Active High'),
            subtitle: Text(_isActiveHigh
                ? 'Component is active when pin is HIGH (3.3V/5V)'
                : 'Component is active when pin is LOW (0V)'),
            value: _isActiveHigh,
            onChanged: (value) {
              setState(() {
                _isActiveHigh = value;
              });
            },
          ),
          const SizedBox(height: 8),
        ],

        // Unit Selection
        if (_selectedComponentType.availableUnits.isNotEmpty) ...[
          DropdownButtonFormField<String>(
            value: _selectedUnit,
            decoration: const InputDecoration(
              labelText: 'Unit',
              hintText: 'Select measurement unit',
            ),
            items: _selectedComponentType.availableUnits.map((unit) {
              return DropdownMenuItem(
                value: unit,
                child: Text(unit),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedUnit = value;
              });
            },
          ),
          const SizedBox(height: 16),
        ],

        // Custom Command (for advanced users)
        if (_selectedComponentType == ComponentType.custom) ...[
          TextFormField(
            controller: _customCommandController,
            decoration: const InputDecoration(
              labelText: 'Custom Command',
              hintText: 'e.g., SERVO_90, MOTOR_SPEED_50',
              helperText: 'Command sent to device when component is activated',
            ),
            validator: (value) {
              if (_selectedComponentType == ComponentType.custom &&
                  (value == null || value.trim().isEmpty)) {
                return 'Please enter a custom command';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
        ],
      ],
    );
  }

  IconData _getDeviceIcon(DeviceType type) {
    switch (type) {
      case DeviceType.esp32Wifi:
      case DeviceType.esp32Bluetooth:
        return MdiIcons.chip;
      case DeviceType.arduinoUno:
      case DeviceType.arduinoNano:
      case DeviceType.arduinoMega:
        return MdiIcons.memory;
    }
  }

  IconData _getComponentIcon(ComponentType type) {
    switch (type) {
      case ComponentType.light:
        return MdiIcons.lightbulb;
      case ComponentType.fan:
        return MdiIcons.fan;
      case ComponentType.servo:
        return MdiIcons.cog;
      case ComponentType.motor:
        return MdiIcons.engine;
      case ComponentType.relay:
        return MdiIcons.electricSwitch;
      case ComponentType.sensor:
        return MdiIcons.thermometer;
      case ComponentType.buzzer:
        return MdiIcons.volumeHigh;
      case ComponentType.led:
        return MdiIcons.ledOn;
      case ComponentType.custom:
        return MdiIcons.wrench;
    }
  }

  String _getDefaultIcon(ComponentType type) {
    switch (type) {
      case ComponentType.light:
        return 'lightbulb';
      case ComponentType.fan:
        return 'fan';
      case ComponentType.servo:
        return 'cog';
      case ComponentType.motor:
        return 'engine';
      case ComponentType.relay:
        return 'toggle-switch';
      case ComponentType.sensor:
        return 'thermometer';
      case ComponentType.buzzer:
        return 'volume-high';
      case ComponentType.led:
        return 'led-outline';
      case ComponentType.custom:
        return 'wrench';
    }
  }

  Future<void> _saveComponent() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedPin == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a pin for the component'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(componentProvider.notifier).addComponent(
        name: _nameController.text.trim(),
        type: _selectedComponentType,
        deviceId: _selectedDeviceId!,
        roomId: widget.roomId,
        pin: _selectedPin!,
        iconName: _selectedIconName,
        isActiveHigh: _isActiveHigh,
        unit: _selectedUnit,
        customCommand: _selectedComponentType == ComponentType.custom
            ? _customCommandController.text.trim()
            : null,
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Component "${_nameController.text.trim()}" added'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
