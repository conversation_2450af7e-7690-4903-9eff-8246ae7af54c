import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/device.dart';
import '../providers/component_provider.dart';
import '../providers/device_provider.dart';
import '../theme/app_theme.dart';

class DeviceCard extends ConsumerWidget {
  final Device device;

  const DeviceCard({
    super.key,
    required this.device,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final components = ref.watch(componentsByDeviceProvider(device.id));
    final theme = Theme.of(context);

    return Card(
      elevation: device.isEnabled ? 2 : 1,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getDeviceTypeColor().withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getDeviceIcon(),
                    color: _getDeviceTypeColor(),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        device.name,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: device.isEnabled
                              ? theme.colorScheme.onSurface
                              : theme.colorScheme.onSurface.withOpacity(0.5),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        device.type.displayName,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusIndicator(theme),
              ],
            ),
            const SizedBox(height: 16),
            _buildConnectionInfo(theme),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  MdiIcons.chip,
                  size: 16,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 4),
                Text(
                  '${components.length} component${components.length != 1 ? 's' : ''}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
                const Spacer(),
                Text(
                  '${device.usedPins.length}/${device.getMaxPins() + 1} pins used',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
            if (!device.isEnabled) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: AppTheme.disabledColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'DEVICE DISABLED',
                  textAlign: TextAlign.center,
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: AppTheme.disabledColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: device.isEnabled ? () => _toggleConnection(ref) : null,
                    icon: Icon(
                      device.isConnected ? MdiIcons.linkOff : MdiIcons.link,
                      size: 16,
                    ),
                    label: Text(device.isConnected ? 'Disconnect' : 'Connect'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: device.isConnected 
                          ? AppTheme.disconnectedColor 
                          : AppTheme.connectedColor,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _showDeviceOptions(context, ref),
                  icon: Icon(MdiIcons.dotsVertical),
                  tooltip: 'Device Options',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(ThemeData theme) {
    if (!device.isEnabled) {
      return Icon(
        Icons.power_off,
        size: 20,
        color: AppTheme.disabledColor,
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: device.isConnected 
                ? AppTheme.connectedColor 
                : AppTheme.disconnectedColor,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          device.isConnected ? 'Connected' : 'Offline',
          style: theme.textTheme.bodySmall?.copyWith(
            color: device.isConnected 
                ? AppTheme.connectedColor 
                : AppTheme.disconnectedColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildConnectionInfo(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            device.connectionType == ConnectionType.bluetooth
                ? MdiIcons.bluetooth
                : MdiIcons.wifi,
            size: 16,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  device.connectionType.displayName,
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  device.connectionString,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getDeviceIcon() {
    switch (device.type) {
      case DeviceType.arduinoHC05:
        return MdiIcons.developerBoard;
      case DeviceType.esp8266Wifi:
        return MdiIcons.wifi;
      case DeviceType.esp32Wifi:
        return MdiIcons.wifi;
      case DeviceType.esp32Bluetooth:
        return MdiIcons.bluetooth;
    }
  }

  Color _getDeviceTypeColor() {
    switch (device.type) {
      case DeviceType.arduinoHC05:
        return Colors.blue;
      case DeviceType.esp8266Wifi:
        return Colors.green;
      case DeviceType.esp32Wifi:
        return Colors.purple;
      case DeviceType.esp32Bluetooth:
        return Colors.orange;
    }
  }

  void _toggleConnection(WidgetRef ref) {
    // Toggle connection status
    ref.read(deviceProvider.notifier).updateDeviceConnectionStatus(
      device.id,
      !device.isConnected,
    );
  }

  void _showDeviceOptions(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: Icon(device.isEnabled ? MdiIcons.power : MdiIcons.powerOn),
            title: Text(device.isEnabled ? 'Disable Device' : 'Enable Device'),
            onTap: () {
              ref.read(deviceProvider.notifier).toggleDeviceEnabled(device.id);
              Navigator.of(context).pop();
            },
          ),
          ListTile(
            leading: Icon(MdiIcons.pencil),
            title: const Text('Edit Device'),
            onTap: () {
              Navigator.of(context).pop();
              // Navigate to edit device screen
            },
          ),
          ListTile(
            leading: Icon(MdiIcons.delete, color: Colors.red),
            title: const Text('Delete Device'),
            onTap: () {
              Navigator.of(context).pop();
              _showDeleteConfirmation(context, ref);
            },
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Device'),
        content: Text(
          'Are you sure you want to delete "${device.name}"? This will also delete all associated components and automations.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(deviceProvider.notifier).deleteDevice(device.id);
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${device.name} deleted')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
