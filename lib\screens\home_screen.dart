import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/room_provider.dart';
import '../providers/device_provider.dart';
import '../widgets/room_card.dart';
import '../widgets/empty_state_widget.dart';
import 'add_room_screen.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final rooms = ref.watch(roomProvider);
    final hasConnectedDevices = ref.watch(hasConnectedDevicesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Arduverse'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.information),
            onPressed: () {
              _showAppInfo(context);
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Refresh room data
          ref.invalidate(roomProvider);
          ref.invalidate(deviceProvider);
        },
        child: rooms.isEmpty
            ? EmptyStateWidget(
                icon: MdiIcons.home,
                title: 'No Rooms Yet',
                message: hasConnectedDevices
                    ? 'Create your first room to start controlling your devices'
                    : 'Connect a device first, then create rooms to organize your components',
                actionText: hasConnectedDevices ? 'Add Room' : 'Add Device',
                onActionPressed: () {
                  if (hasConnectedDevices) {
                    _navigateToAddRoom(context);
                  } else {
                    // Navigate to devices tab
                    ref.read(currentTabProvider.notifier).state = 1;
                  }
                },
              )
            : Padding(
                padding: const EdgeInsets.all(16.0),
                child: MasonryGridView.count(
                  crossAxisCount: _getCrossAxisCount(context),
                  mainAxisSpacing: 16,
                  crossAxisSpacing: 16,
                  itemCount: rooms.length,
                  itemBuilder: (context, index) {
                    return RoomCard(room: rooms[index]);
                  },
                ),
              ),
      ),
      floatingActionButton: hasConnectedDevices
          ? FloatingActionButton(
              onPressed: () => _navigateToAddRoom(context),
              tooltip: 'Add Room',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    if (width > 600) return 2;
    return 1;
  }

  void _navigateToAddRoom(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddRoomScreen(),
      ),
    );
  }

  void _showAppInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              MdiIcons.information,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(width: 8),
            const Text('About Arduverse'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Professional IoT controller app for Arduino, ESP32, and ESP8266 devices.',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            Text(
              'Features:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('• Control devices via Bluetooth and WiFi'),
            Text('• Organize components into rooms'),
            Text('• Create smart automations'),
            Text('• Real-time device monitoring'),
            SizedBox(height: 16),
            Text(
              'Developer: SK Raihan',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Text('Electronics Engineer & Maker'),
            Text('Founder of SKR Electronics Lab'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

// Import the currentTabProvider from main_screen.dart
final currentTabProvider = StateProvider<int>((ref) => 0);
