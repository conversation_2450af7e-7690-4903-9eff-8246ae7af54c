// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'component.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ComponentAdapter extends TypeAdapter<Component> {
  @override
  final int typeId = 3;

  @override
  Component read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Component(
      id: fields[0] as String,
      name: fields[1] as String,
      type: fields[2] as ComponentType,
      deviceId: fields[3] as String,
      pin: fields[4] as int,
      iconName: fields[5] as String,
      isActiveHigh: fields[6] as bool,
      unit: fields[7] as String?,
      customCommand: fields[8] as String?,
      currentValue: fields[9] as double?,
      isEnabled: fields[10] as bool,
      createdAt: fields[11] as DateTime,
      updatedAt: fields[12] as DateTime,
      settings: (fields[13] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, Component obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.deviceId)
      ..writeByte(4)
      ..write(obj.pin)
      ..writeByte(5)
      ..write(obj.iconName)
      ..writeByte(6)
      ..write(obj.isActiveHigh)
      ..writeByte(7)
      ..write(obj.unit)
      ..writeByte(8)
      ..write(obj.customCommand)
      ..writeByte(9)
      ..write(obj.currentValue)
      ..writeByte(10)
      ..write(obj.isEnabled)
      ..writeByte(11)
      ..write(obj.createdAt)
      ..writeByte(12)
      ..write(obj.updatedAt)
      ..writeByte(13)
      ..write(obj.settings);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ComponentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ComponentTypeAdapter extends TypeAdapter<ComponentType> {
  @override
  final int typeId = 4;

  @override
  ComponentType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ComponentType.light;
      case 1:
        return ComponentType.relay;
      case 2:
        return ComponentType.pwm;
      case 3:
        return ComponentType.rgb;
      case 4:
        return ComponentType.pushButton;
      case 5:
        return ComponentType.digitalSensor;
      case 6:
        return ComponentType.mic;
      case 7:
        return ComponentType.terminal;
      case 8:
        return ComponentType.dht11;
      case 9:
        return ComponentType.ldr;
      case 10:
        return ComponentType.pir;
      case 11:
        return ComponentType.ultrasonic;
      default:
        return ComponentType.light;
    }
  }

  @override
  void write(BinaryWriter writer, ComponentType obj) {
    switch (obj) {
      case ComponentType.light:
        writer.writeByte(0);
        break;
      case ComponentType.relay:
        writer.writeByte(1);
        break;
      case ComponentType.pwm:
        writer.writeByte(2);
        break;
      case ComponentType.rgb:
        writer.writeByte(3);
        break;
      case ComponentType.pushButton:
        writer.writeByte(4);
        break;
      case ComponentType.digitalSensor:
        writer.writeByte(5);
        break;
      case ComponentType.mic:
        writer.writeByte(6);
        break;
      case ComponentType.terminal:
        writer.writeByte(7);
        break;
      case ComponentType.dht11:
        writer.writeByte(8);
        break;
      case ComponentType.ldr:
        writer.writeByte(9);
        break;
      case ComponentType.pir:
        writer.writeByte(10);
        break;
      case ComponentType.ultrasonic:
        writer.writeByte(11);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ComponentTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Component _$ComponentFromJson(Map<String, dynamic> json) => Component(
      id: json['id'] as String,
      name: json['name'] as String,
      type: $enumDecode(_$ComponentTypeEnumMap, json['type']),
      deviceId: json['deviceId'] as String,
      pin: (json['pin'] as num).toInt(),
      iconName: json['iconName'] as String,
      isActiveHigh: json['isActiveHigh'] as bool? ?? true,
      unit: json['unit'] as String?,
      customCommand: json['customCommand'] as String?,
      currentValue: (json['currentValue'] as num?)?.toDouble(),
      isEnabled: json['isEnabled'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      settings: json['settings'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ComponentToJson(Component instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': _$ComponentTypeEnumMap[instance.type]!,
      'deviceId': instance.deviceId,
      'pin': instance.pin,
      'iconName': instance.iconName,
      'isActiveHigh': instance.isActiveHigh,
      'unit': instance.unit,
      'customCommand': instance.customCommand,
      'currentValue': instance.currentValue,
      'isEnabled': instance.isEnabled,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'settings': instance.settings,
    };

const _$ComponentTypeEnumMap = {
  ComponentType.light: 'light',
  ComponentType.relay: 'relay',
  ComponentType.pwm: 'pwm',
  ComponentType.rgb: 'rgb',
  ComponentType.pushButton: 'pushButton',
  ComponentType.digitalSensor: 'digitalSensor',
  ComponentType.mic: 'mic',
  ComponentType.terminal: 'terminal',
  ComponentType.dht11: 'dht11',
  ComponentType.ldr: 'ldr',
  ComponentType.pir: 'pir',
  ComponentType.ultrasonic: 'ultrasonic',
};
