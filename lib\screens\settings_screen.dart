import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/app_settings_provider.dart';
import '../widgets/settings_tile.dart';
import 'about_screen.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settings = ref.watch(appSettingsProvider);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        children: [
          // Theme Settings
          _buildSectionHeader(context, 'Appearance'),
          SettingsTile(
            icon: MdiIcons.palette,
            title: 'Theme',
            subtitle: settings.themeModeDisplayName,
            onTap: () => _showThemeDialog(context, ref),
          ),
          
          const Divider(),
          
          // Connection Settings
          _buildSectionHeader(context, 'Connection'),
          SettingsTile(
            icon: MdiIcons.wifi,
            title: 'Default WiFi Settings',
            subtitle: '${settings.defaultWifiIp}:${settings.defaultWifiPort}',
            onTap: () => _showWifiSettingsDialog(context, ref),
          ),
          SettingsTile(
            icon: MdiIcons.timer,
            title: 'Connection Timeout',
            subtitle: '${settings.connectionTimeout} seconds',
            onTap: () => _showTimeoutDialog(context, ref),
          ),
          SwitchListTile(
            secondary: Icon(MdiIcons.autorenew),
            title: const Text('Auto-connect Devices'),
            subtitle: const Text('Automatically connect to devices on app start'),
            value: settings.autoConnectDevices,
            onChanged: (value) {
              ref.read(appSettingsProvider.notifier).updateAutoConnectDevices(value);
            },
          ),
          
          const Divider(),
          
          // App Settings
          _buildSectionHeader(context, 'App Settings'),
          SwitchListTile(
            secondary: Icon(MdiIcons.bell),
            title: const Text('Notifications'),
            subtitle: const Text('Enable app notifications'),
            value: settings.notificationsEnabled,
            onChanged: (value) {
              ref.read(appSettingsProvider.notifier).updateNotificationsEnabled(value);
            },
          ),
          SwitchListTile(
            secondary: Icon(MdiIcons.vibrate),
            title: const Text('Haptic Feedback'),
            subtitle: const Text('Vibrate on button presses'),
            value: settings.hapticFeedback,
            onChanged: (value) {
              ref.read(appSettingsProvider.notifier).updateHapticFeedback(value);
            },
          ),
          SwitchListTile(
            secondary: Icon(MdiIcons.phoneInTalk),
            title: const Text('Keep Screen On'),
            subtitle: const Text('Prevent screen from turning off'),
            value: settings.keepScreenOn,
            onChanged: (value) {
              ref.read(appSettingsProvider.notifier).updateKeepScreenOn(value);
            },
          ),
          SettingsTile(
            icon: MdiIcons.refresh,
            title: 'Refresh Interval',
            subtitle: '${settings.refreshInterval} seconds',
            onTap: () => _showRefreshIntervalDialog(context, ref),
          ),
          
          const Divider(),
          
          // Debug Settings
          _buildSectionHeader(context, 'Debug'),
          SwitchListTile(
            secondary: Icon(MdiIcons.bug),
            title: const Text('Show Debug Info'),
            subtitle: const Text('Display debug information'),
            value: settings.showDebugInfo,
            onChanged: (value) {
              ref.read(appSettingsProvider.notifier).updateShowDebugInfo(value);
            },
          ),
          
          const Divider(),
          
          // About & Support
          _buildSectionHeader(context, 'About & Support'),
          SettingsTile(
            icon: MdiIcons.information,
            title: 'About Arduverse',
            subtitle: 'App info, developer details, and social links',
            onTap: () => _navigateToAbout(context),
          ),
          SettingsTile(
            icon: MdiIcons.email,
            title: 'Support',
            subtitle: 'Get help and report issues',
            onTap: () => _launchEmail(),
          ),
          SettingsTile(
            icon: MdiIcons.fileDocument,
            title: 'Documentation',
            subtitle: 'User guide and troubleshooting',
            onTap: () => _launchDocumentation(),
          ),
          SettingsTile(
            icon: MdiIcons.shield,
            title: 'Privacy Policy',
            subtitle: 'How we handle your data',
            onTap: () => _showPrivacyPolicy(context),
          ),
          
          const Divider(),
          
          // Data Management
          _buildSectionHeader(context, 'Data'),
          SettingsTile(
            icon: MdiIcons.backup,
            title: 'Export Data',
            subtitle: 'Backup your rooms and devices',
            onTap: () => _showExportDialog(context),
          ),
          SettingsTile(
            icon: MdiIcons.restore,
            title: 'Import Data',
            subtitle: 'Restore from backup',
            onTap: () => _showImportDialog(context),
          ),
          SettingsTile(
            icon: MdiIcons.deleteForever,
            title: 'Reset All Data',
            subtitle: 'Delete all rooms, devices, and settings',
            onTap: () => _showResetDialog(context, ref),
            textColor: Colors.red,
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _showThemeDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<ThemeMode>(
              title: const Text('Light'),
              value: ThemeMode.light,
              groupValue: ref.read(appSettingsProvider).themeMode,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appSettingsProvider.notifier).updateThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('Dark'),
              value: ThemeMode.dark,
              groupValue: ref.read(appSettingsProvider).themeMode,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appSettingsProvider.notifier).updateThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('System'),
              value: ThemeMode.system,
              groupValue: ref.read(appSettingsProvider).themeMode,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appSettingsProvider.notifier).updateThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showWifiSettingsDialog(BuildContext context, WidgetRef ref) {
    final settings = ref.read(appSettingsProvider);
    final ipController = TextEditingController(text: settings.defaultWifiIp);
    final portController = TextEditingController(text: settings.defaultWifiPort.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Default WiFi Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: ipController,
              decoration: const InputDecoration(
                labelText: 'IP Address',
                hintText: '***********',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: portController,
              decoration: const InputDecoration(
                labelText: 'Port',
                hintText: '80',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              try {
                final ip = ipController.text.trim();
                final port = int.parse(portController.text.trim());
                ref.read(appSettingsProvider.notifier).updateDefaultWifiSettings(ip, port);
                Navigator.of(context).pop();
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Invalid IP address or port')),
                );
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showTimeoutDialog(BuildContext context, WidgetRef ref) {
    // Implementation for timeout dialog
  }

  void _showRefreshIntervalDialog(BuildContext context, WidgetRef ref) {
    // Implementation for refresh interval dialog
  }

  void _navigateToAbout(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AboutScreen()),
    );
  }

  void _launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: 'subject=Arduverse Support Request',
    );
    
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  void _launchDocumentation() async {
    const url = 'https://www.youtube.com/@skr_electronics_lab';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  void _showPrivacyPolicy(BuildContext context) {
    // Implementation for privacy policy
  }

  void _showExportDialog(BuildContext context) {
    // Implementation for export dialog
  }

  void _showImportDialog(BuildContext context) {
    // Implementation for import dialog
  }

  void _showResetDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset All Data'),
        content: const Text(
          'This will permanently delete all your rooms, devices, automations, and settings. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(appSettingsProvider.notifier).resetToDefaults();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('All data has been reset')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
