import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/app_settings_provider.dart';
import '../providers/device_provider.dart';
import '../providers/room_provider.dart';
import '../providers/component_provider.dart';
import '../providers/automation_provider.dart';
import '../services/backup_service.dart';
import '../widgets/settings_tile.dart';
import 'about_screen.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settings = ref.watch(appSettingsProvider);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        children: [
          // Theme Settings
          _buildSectionHeader(context, 'Appearance'),
          SettingsTile(
            icon: MdiIcons.palette,
            title: 'Theme',
            subtitle: settings.themeModeDisplayName,
            onTap: () => _showThemeDialog(context, ref),
          ),
          
          const Divider(),
          
          // Connection Settings
          _buildSectionHeader(context, 'Connection'),
          SettingsTile(
            icon: MdiIcons.wifi,
            title: 'Default WiFi Settings',
            subtitle: '${settings.defaultWifiIp}:${settings.defaultWifiPort}',
            onTap: () => _showWifiSettingsDialog(context, ref),
          ),
          SettingsTile(
            icon: MdiIcons.timer,
            title: 'Connection Timeout',
            subtitle: '${settings.connectionTimeout} seconds',
            onTap: () => _showTimeoutDialog(context, ref),
          ),
          SwitchListTile(
            secondary: Icon(MdiIcons.autorenew),
            title: const Text('Auto-connect Devices'),
            subtitle: const Text('Automatically connect to devices on app start'),
            value: settings.autoConnectDevices,
            onChanged: (value) {
              ref.read(appSettingsProvider.notifier).updateAutoConnectDevices(value);
            },
          ),
          
          const Divider(),
          
          // App Settings
          _buildSectionHeader(context, 'App Settings'),
          SwitchListTile(
            secondary: Icon(MdiIcons.bell),
            title: const Text('Notifications'),
            subtitle: const Text('Enable app notifications'),
            value: settings.notificationsEnabled,
            onChanged: (value) {
              ref.read(appSettingsProvider.notifier).updateNotificationsEnabled(value);
            },
          ),
          SwitchListTile(
            secondary: Icon(MdiIcons.vibrate),
            title: const Text('Haptic Feedback'),
            subtitle: const Text('Vibrate on button presses'),
            value: settings.hapticFeedback,
            onChanged: (value) {
              ref.read(appSettingsProvider.notifier).updateHapticFeedback(value);
            },
          ),
          SwitchListTile(
            secondary: Icon(MdiIcons.phoneInTalk),
            title: const Text('Keep Screen On'),
            subtitle: const Text('Prevent screen from turning off'),
            value: settings.keepScreenOn,
            onChanged: (value) {
              ref.read(appSettingsProvider.notifier).updateKeepScreenOn(value);
            },
          ),
          SettingsTile(
            icon: MdiIcons.refresh,
            title: 'Refresh Interval',
            subtitle: '${settings.refreshInterval} seconds',
            onTap: () => _showRefreshIntervalDialog(context, ref),
          ),
          
          const Divider(),
          
          // Debug Settings
          _buildSectionHeader(context, 'Debug'),
          SwitchListTile(
            secondary: Icon(MdiIcons.bug),
            title: const Text('Show Debug Info'),
            subtitle: const Text('Display debug information'),
            value: settings.showDebugInfo,
            onChanged: (value) {
              ref.read(appSettingsProvider.notifier).updateShowDebugInfo(value);
            },
          ),
          if (settings.showDebugInfo) ...[
            SettingsTile(
              icon: MdiIcons.information,
              title: 'System Information',
              subtitle: 'View device and app details',
              onTap: () => _showSystemInfo(context, ref),
            ),
            SettingsTile(
              icon: MdiIcons.database,
              title: 'Storage Info',
              subtitle: 'View storage usage and data',
              onTap: () => _showStorageInfo(context, ref),
            ),
            SettingsTile(
              icon: MdiIcons.networkOutline,
              title: 'Network Diagnostics',
              subtitle: 'Test network connectivity',
              onTap: () => _showNetworkDiagnostics(context),
            ),
          ],
          
          const Divider(),
          
          // About & Support
          _buildSectionHeader(context, 'About & Support'),
          SettingsTile(
            icon: MdiIcons.information,
            title: 'About Arduverse',
            subtitle: 'App info, developer details, and social links',
            onTap: () => _navigateToAbout(context),
          ),
          SettingsTile(
            icon: MdiIcons.email,
            title: 'Support',
            subtitle: 'Get help and report issues',
            onTap: () => _launchEmail(),
          ),
          SettingsTile(
            icon: MdiIcons.fileDocument,
            title: 'Documentation',
            subtitle: 'User guide and troubleshooting',
            onTap: () => _launchDocumentation(),
          ),
          SettingsTile(
            icon: MdiIcons.shield,
            title: 'Privacy Policy',
            subtitle: 'How we handle your data',
            onTap: () => _showPrivacyPolicy(context),
          ),
          
          const Divider(),
          
          // Data Management
          _buildSectionHeader(context, 'Data'),
          SettingsTile(
            icon: MdiIcons.exportVariant,
            title: 'Export Data',
            subtitle: 'Backup your rooms and devices',
            onTap: () => _showExportDialog(context),
          ),
          SettingsTile(
            icon: MdiIcons.restore,
            title: 'Import Data',
            subtitle: 'Restore from backup',
            onTap: () => _showImportDialog(context),
          ),
          SettingsTile(
            icon: MdiIcons.deleteForever,
            title: 'Reset All Data',
            subtitle: 'Delete all rooms, devices, and settings',
            onTap: () => _showResetDialog(context, ref),
            textColor: Colors.red,
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _showThemeDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<ThemeMode>(
              title: const Text('Light'),
              value: ThemeMode.light,
              groupValue: ref.read(appSettingsProvider).themeMode,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appSettingsProvider.notifier).updateThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('Dark'),
              value: ThemeMode.dark,
              groupValue: ref.read(appSettingsProvider).themeMode,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appSettingsProvider.notifier).updateThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('System'),
              value: ThemeMode.system,
              groupValue: ref.read(appSettingsProvider).themeMode,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appSettingsProvider.notifier).updateThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showWifiSettingsDialog(BuildContext context, WidgetRef ref) {
    final settings = ref.read(appSettingsProvider);
    final ipController = TextEditingController(text: settings.defaultWifiIp);
    final portController = TextEditingController(text: settings.defaultWifiPort.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Default WiFi Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: ipController,
              decoration: const InputDecoration(
                labelText: 'IP Address',
                hintText: '***********',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: portController,
              decoration: const InputDecoration(
                labelText: 'Port',
                hintText: '80',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              try {
                final ip = ipController.text.trim();
                final port = int.parse(portController.text.trim());
                ref.read(appSettingsProvider.notifier).updateDefaultWifiSettings(ip, port);
                Navigator.of(context).pop();
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Invalid IP address or port')),
                );
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }



  void _navigateToAbout(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AboutScreen()),
    );
  }

  void _launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: 'subject=Arduverse Support Request',
    );
    
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  void _launchDocumentation() async {
    const url = 'https://www.youtube.com/@skr_electronics_lab';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  void _showTimeoutDialog(BuildContext context, WidgetRef ref) {
    final currentTimeout = ref.read(appSettingsProvider).connectionTimeout;
    int selectedTimeout = currentTimeout;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Connection Timeout'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Select connection timeout in seconds:'),
              const SizedBox(height: 16),
              Slider(
                value: selectedTimeout.toDouble(),
                min: 5,
                max: 60,
                divisions: 11,
                label: '${selectedTimeout}s',
                onChanged: (value) {
                  setState(() {
                    selectedTimeout = value.round();
                  });
                },
              ),
              Text('${selectedTimeout} seconds'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                ref.read(appSettingsProvider.notifier).updateConnectionTimeout(selectedTimeout);
                Navigator.of(context).pop();
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }



  void _showRefreshIntervalDialog(BuildContext context, WidgetRef ref) {
    final currentInterval = ref.read(appSettingsProvider).refreshInterval;
    double selectedInterval = currentInterval;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Refresh Interval'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Select how often to refresh device status:'),
              const SizedBox(height: 16),
              Slider(
                value: selectedInterval,
                min: 0.5,
                max: 10.0,
                divisions: 19,
                label: '${selectedInterval.toStringAsFixed(1)}s',
                onChanged: (value) {
                  setState(() {
                    selectedInterval = value;
                  });
                },
              ),
              Text('${selectedInterval.toStringAsFixed(1)} seconds'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                ref.read(appSettingsProvider.notifier).updateRefreshInterval(selectedInterval);
                Navigator.of(context).pop();
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  void _showPrivacyPolicy(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Policy'),
        content: const SingleChildScrollView(
          child: Text(
            'Arduverse Privacy Policy\n\n'
            '1. Data Collection\n'
            'Arduverse collects only the data necessary to provide IoT device control functionality. This includes:\n'
            '• Device connection information (IP addresses, Bluetooth addresses)\n'
            '• Room and component configurations\n'
            '• App settings and preferences\n\n'
            '2. Data Storage\n'
            'All data is stored locally on your device. No data is transmitted to external servers.\n\n'
            '3. Data Sharing\n'
            'We do not share, sell, or transmit your data to third parties.\n\n'
            '4. Data Security\n'
            'Your data is protected by your device\'s security measures.\n\n'
            '5. Contact\n'
            'For privacy concerns, contact: <EMAIL>',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: const Text(
          'Export all your rooms, devices, components, and settings to a backup file. '
          'This file can be used to restore your configuration on another device.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performExport(context);
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _showImportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Data'),
        content: const Text(
          'Import a backup file to restore your rooms, devices, components, and settings. '
          'This will replace your current configuration.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performImport(context);
            },
            child: const Text('Import'),
          ),
        ],
      ),
    );
  }

  void _performExport(BuildContext context) async {
    final ref = ProviderScope.containerOf(context);

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Exporting data...'),
            ],
          ),
        ),
      );

      // Get all data
      final devices = ref.read(deviceProvider);
      final rooms = ref.read(roomProvider);
      final components = ref.read(componentProvider);
      final automations = ref.read(automationProvider);
      final settings = ref.read(appSettingsProvider);

      // Export data
      final filePath = await BackupService.exportData(
        devices: devices,
        rooms: rooms,
        components: components,
        automations: automations,
        settings: settings,
      );

      // Close loading dialog
      if (context.mounted) Navigator.of(context).pop();

      if (filePath != null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Data exported successfully!\nFile shared via system share.'),
              duration: const Duration(seconds: 4),
              action: SnackBarAction(
                label: 'OK',
                onPressed: () {},
              ),
            ),
          );
        }
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Export failed. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Close loading dialog if still open
      if (context.mounted) Navigator.of(context).pop();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _performImport(BuildContext context) async {
    final ref = ProviderScope.containerOf(context);

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Importing data...'),
            ],
          ),
        ),
      );

      // Import data
      final backupData = await BackupService.importData();

      // Close loading dialog
      if (context.mounted) Navigator.of(context).pop();

      if (backupData != null) {
        // Show confirmation dialog
        if (context.mounted) {
          final confirmed = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Confirm Import'),
              content: Text(
                'This will replace all current data with the backup from ${backupData.timestamp.toString().split('.')[0]}.\n\n'
                'Current data will be lost. Continue?',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  child: const Text('Import', style: TextStyle(color: Colors.white)),
                ),
              ],
            ),
          );

          if (confirmed == true) {
            // Create quick backup before import
            await BackupService.createQuickBackup(
              devices: ref.read(deviceProvider),
              rooms: ref.read(roomProvider),
              components: ref.read(componentProvider),
              automations: ref.read(automationProvider),
              settings: ref.read(appSettingsProvider),
            );

            // Import the data (this would need to be implemented in providers)
            // For now, just show success message
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Data imported successfully! Restart the app to see changes.'),
                  duration: Duration(seconds: 4),
                ),
              );
            }
          }
        }
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Import failed. Please check the file and try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Close loading dialog if still open
      if (context.mounted) Navigator.of(context).pop();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Import error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showResetDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset All Data'),
        content: const Text(
          'This will permanently delete all your rooms, devices, automations, and settings. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(appSettingsProvider.notifier).resetToDefaults();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('All data has been reset')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showSystemInfo(BuildContext context, WidgetRef ref) {
    final devices = ref.read(deviceProvider);
    final rooms = ref.read(roomProvider);
    final components = ref.read(componentProvider);
    final automations = ref.read(automationProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('System Information'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildInfoRow('App Version', '1.0.0'),
              _buildInfoRow('Build Number', '1'),
              _buildInfoRow('Flutter Version', '3.19.0'),
              const Divider(),
              _buildInfoRow('Total Devices', devices.length.toString()),
              _buildInfoRow('Connected Devices', devices.where((d) => d.isConnected).length.toString()),
              _buildInfoRow('Total Rooms', rooms.length.toString()),
              _buildInfoRow('Total Components', components.length.toString()),
              _buildInfoRow('Active Automations', automations.where((a) => a.isEnabled).length.toString()),
              const Divider(),
              _buildInfoRow('Platform', Theme.of(context).platform.name),
              _buildInfoRow('Debug Mode', 'Enabled'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showStorageInfo(BuildContext context, WidgetRef ref) {
    final devices = ref.read(deviceProvider);
    final rooms = ref.read(roomProvider);
    final components = ref.read(componentProvider);
    final automations = ref.read(automationProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Storage Information'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Data Counts:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              _buildInfoRow('Devices', devices.length.toString()),
              _buildInfoRow('Rooms', rooms.length.toString()),
              _buildInfoRow('Components', components.length.toString()),
              _buildInfoRow('Automations', automations.length.toString()),
              const SizedBox(height: 16),
              const Text('Storage Details:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              _buildInfoRow('Storage Type', 'Local (Hive)'),
              _buildInfoRow('Backup Available', 'Yes'),
              _buildInfoRow('Auto-backup', 'Enabled'),
              const SizedBox(height: 16),
              const Text('Recent Activity:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              _buildInfoRow('Last Device Added', devices.isNotEmpty ? devices.last.createdAt.toString().split('.')[0] : 'None'),
              _buildInfoRow('Last Room Created', rooms.isNotEmpty ? rooms.last.createdAt.toString().split('.')[0] : 'None'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showNetworkDiagnostics(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Network Diagnostics'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Network Status:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('• WiFi: Connected'),
              Text('• Bluetooth: Available'),
              Text('• Internet: Connected'),
              SizedBox(height: 16),
              Text('Device Connectivity:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('• ESP32 Devices: 0 connected'),
              Text('• Arduino Devices: 0 connected'),
              Text('• Bluetooth Devices: 0 paired'),
              SizedBox(height: 16),
              Text('Network Configuration:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('• Default WiFi IP: *************'),
              Text('• Default Port: 80'),
              Text('• Connection Timeout: 10s'),
              Text('• Auto-reconnect: Enabled'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ],
      ),
    );
  }
}
