// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DeviceAdapter extends TypeAdapter<Device> {
  @override
  final int typeId = 0;

  @override
  Device read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Device(
      id: fields[0] as String,
      name: fields[1] as String,
      type: fields[2] as DeviceType,
      connectionType: fields[3] as ConnectionType,
      bluetoothAddress: fields[4] as String?,
      wifiIp: fields[5] as String?,
      wifiPort: fields[6] as int?,
      isEnabled: fields[7] as bool,
      isConnected: fields[8] as bool,
      createdAt: fields[9] as DateTime,
      updatedAt: fields[10] as DateTime,
      usedPins: (fields[11] as List).cast<int>(),
    );
  }

  @override
  void write(BinaryWriter writer, Device obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.connectionType)
      ..writeByte(4)
      ..write(obj.bluetoothAddress)
      ..writeByte(5)
      ..write(obj.wifiIp)
      ..writeByte(6)
      ..write(obj.wifiPort)
      ..writeByte(7)
      ..write(obj.isEnabled)
      ..writeByte(8)
      ..write(obj.isConnected)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.updatedAt)
      ..writeByte(11)
      ..write(obj.usedPins);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DeviceTypeAdapter extends TypeAdapter<DeviceType> {
  @override
  final int typeId = 1;

  @override
  DeviceType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return DeviceType.arduinoHC05;
      case 1:
        return DeviceType.esp8266Wifi;
      case 2:
        return DeviceType.esp32Wifi;
      case 3:
        return DeviceType.esp32Bluetooth;
      default:
        return DeviceType.arduinoHC05;
    }
  }

  @override
  void write(BinaryWriter writer, DeviceType obj) {
    switch (obj) {
      case DeviceType.arduinoHC05:
        writer.writeByte(0);
        break;
      case DeviceType.esp8266Wifi:
        writer.writeByte(1);
        break;
      case DeviceType.esp32Wifi:
        writer.writeByte(2);
        break;
      case DeviceType.esp32Bluetooth:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ConnectionTypeAdapter extends TypeAdapter<ConnectionType> {
  @override
  final int typeId = 2;

  @override
  ConnectionType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ConnectionType.bluetooth;
      case 1:
        return ConnectionType.wifi;
      default:
        return ConnectionType.bluetooth;
    }
  }

  @override
  void write(BinaryWriter writer, ConnectionType obj) {
    switch (obj) {
      case ConnectionType.bluetooth:
        writer.writeByte(0);
        break;
      case ConnectionType.wifi:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ConnectionTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Device _$DeviceFromJson(Map<String, dynamic> json) => Device(
      id: json['id'] as String,
      name: json['name'] as String,
      type: $enumDecode(_$DeviceTypeEnumMap, json['type']),
      connectionType:
          $enumDecode(_$ConnectionTypeEnumMap, json['connectionType']),
      bluetoothAddress: json['bluetoothAddress'] as String?,
      wifiIp: json['wifiIp'] as String?,
      wifiPort: (json['wifiPort'] as num?)?.toInt(),
      isEnabled: json['isEnabled'] as bool? ?? true,
      isConnected: json['isConnected'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      usedPins: (json['usedPins'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
    );

Map<String, dynamic> _$DeviceToJson(Device instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': _$DeviceTypeEnumMap[instance.type]!,
      'connectionType': _$ConnectionTypeEnumMap[instance.connectionType]!,
      'bluetoothAddress': instance.bluetoothAddress,
      'wifiIp': instance.wifiIp,
      'wifiPort': instance.wifiPort,
      'isEnabled': instance.isEnabled,
      'isConnected': instance.isConnected,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'usedPins': instance.usedPins,
    };

const _$DeviceTypeEnumMap = {
  DeviceType.arduinoHC05: 'arduinoHC05',
  DeviceType.esp8266Wifi: 'esp8266Wifi',
  DeviceType.esp32Wifi: 'esp32Wifi',
  DeviceType.esp32Bluetooth: 'esp32Bluetooth',
};

const _$ConnectionTypeEnumMap = {
  ConnectionType.bluetooth: 'bluetooth',
  ConnectionType.wifi: 'wifi',
};
