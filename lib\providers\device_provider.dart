import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/device.dart';
import '../services/storage_service.dart';

class DeviceNotifier extends StateNotifier<List<Device>> {
  DeviceNotifier(this._storageService) : super([]) {
    _loadDevices();
  }

  final StorageService _storageService;
  static const _uuid = Uuid();

  Future<void> _loadDevices() async {
    try {
      final devices = await _storageService.getDevices();
      state = devices;
    } catch (e) {
      // Handle error - for now just keep empty list
      state = [];
    }
  }

  Future<void> addDevice({
    required String name,
    required DeviceType type,
    required ConnectionType connectionType,
    String? bluetoothAddress,
    String? wifiIp,
    int? wifiPort,
  }) async {
    // Validate input
    if (name.trim().isEmpty) {
      throw ArgumentError('Device name cannot be empty');
    }

    if (connectionType == ConnectionType.bluetooth && bluetoothAddress == null) {
      throw ArgumentError('Bluetooth address is required for Bluetooth devices');
    }

    if (connectionType == ConnectionType.wifi) {
      if (wifiIp == null || wifiPort == null) {
        throw ArgumentError('WiFi IP and port are required for WiFi devices');
      }
      if (!_isValidIpAddress(wifiIp) || !_isValidPort(wifiPort)) {
        throw ArgumentError('Invalid WiFi IP or port');
      }
    }

    // Check for duplicate names
    if (state.any((device) => device.name.toLowerCase() == name.toLowerCase())) {
      throw ArgumentError('A device with this name already exists');
    }

    // Check for duplicate connection strings
    final connectionString = connectionType == ConnectionType.bluetooth
        ? bluetoothAddress!
        : '$wifiIp:$wifiPort';
    
    if (state.any((device) => device.connectionString == connectionString)) {
      throw ArgumentError('A device with this connection already exists');
    }

    final now = DateTime.now();
    final device = Device(
      id: _uuid.v4(),
      name: name.trim(),
      type: type,
      connectionType: connectionType,
      bluetoothAddress: bluetoothAddress,
      wifiIp: wifiIp,
      wifiPort: wifiPort,
      createdAt: now,
      updatedAt: now,
    );

    state = [...state, device];
    await _storageService.saveDevice(device);
  }

  Future<void> updateDevice(Device updatedDevice) async {
    final index = state.indexWhere((device) => device.id == updatedDevice.id);
    if (index == -1) {
      throw ArgumentError('Device not found');
    }

    final newState = [...state];
    newState[index] = updatedDevice.copyWith(updatedAt: DateTime.now());
    state = newState;
    
    await _storageService.saveDevice(newState[index]);
  }

  Future<void> deleteDevice(String deviceId) async {
    final device = state.firstWhere(
      (device) => device.id == deviceId,
      orElse: () => throw ArgumentError('Device not found'),
    );

    state = state.where((device) => device.id != deviceId).toList();
    await _storageService.deleteDevice(deviceId);
  }

  Future<void> toggleDeviceEnabled(String deviceId) async {
    final device = getDeviceById(deviceId);
    if (device == null) return;

    await updateDevice(device.copyWith(isEnabled: !device.isEnabled));
  }

  Future<void> updateDeviceConnectionStatus(String deviceId, bool isConnected) async {
    final device = getDeviceById(deviceId);
    if (device == null) return;

    await updateDevice(device.copyWith(isConnected: isConnected));
  }

  Future<void> updateDeviceUsedPins(String deviceId, List<int> usedPins) async {
    final device = getDeviceById(deviceId);
    if (device == null) return;

    await updateDevice(device.copyWith(usedPins: usedPins));
  }

  Device? getDeviceById(String deviceId) {
    try {
      return state.firstWhere((device) => device.id == deviceId);
    } catch (e) {
      return null;
    }
  }

  List<Device> getDevicesByType(DeviceType type) {
    return state.where((device) => device.type == type).toList();
  }

  List<Device> getDevicesByConnectionType(ConnectionType connectionType) {
    return state.where((device) => device.connectionType == connectionType).toList();
  }

  List<Device> getConnectedDevices() {
    return state.where((device) => device.isConnected && device.isEnabled).toList();
  }

  List<Device> getEnabledDevices() {
    return state.where((device) => device.isEnabled).toList();
  }

  bool isPinAvailable(String deviceId, int pin) {
    final device = getDeviceById(deviceId);
    return device?.isPinAvailable(pin) ?? false;
  }

  List<int> getAvailablePins(String deviceId) {
    final device = getDeviceById(deviceId);
    return device?.getAvailablePins() ?? [];
  }

  bool _isValidIpAddress(String ip) {
    final parts = ip.split('.');
    if (parts.length != 4) return false;
    
    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) return false;
    }
    return true;
  }

  bool _isValidPort(int port) {
    return port > 0 && port <= 65535;
  }
}

final deviceProvider = StateNotifierProvider<DeviceNotifier, List<Device>>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return DeviceNotifier(storageService);
});

// Convenience providers
final connectedDevicesProvider = Provider<List<Device>>((ref) {
  final devices = ref.watch(deviceProvider);
  return devices.where((device) => device.isConnected && device.isEnabled).toList();
});

final enabledDevicesProvider = Provider<List<Device>>((ref) {
  final devices = ref.watch(deviceProvider);
  return devices.where((device) => device.isEnabled).toList();
});

final bluetoothDevicesProvider = Provider<List<Device>>((ref) {
  final devices = ref.watch(deviceProvider);
  return devices.where((device) => device.connectionType == ConnectionType.bluetooth).toList();
});

final wifiDevicesProvider = Provider<List<Device>>((ref) {
  final devices = ref.watch(deviceProvider);
  return devices.where((device) => device.connectionType == ConnectionType.wifi).toList();
});

// Provider for getting a specific device by ID
final deviceByIdProvider = Provider.family<Device?, String>((ref, deviceId) {
  final devices = ref.watch(deviceProvider);
  try {
    return devices.firstWhere((device) => device.id == deviceId);
  } catch (e) {
    return null;
  }
});

// Provider for checking if any devices are connected
final hasConnectedDevicesProvider = Provider<bool>((ref) {
  final connectedDevices = ref.watch(connectedDevicesProvider);
  return connectedDevices.isNotEmpty;
});
