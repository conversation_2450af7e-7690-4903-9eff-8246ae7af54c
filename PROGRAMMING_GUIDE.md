# Arduverse Programming Guide
## Complete Tutorial for Arduino & ESP32 Development

### Table of Contents
1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Hardware Setup](#hardware-setup)
4. [Arduino IDE Setup](#arduino-ide-setup)
5. [Basic Programming Concepts](#basic-programming-concepts)
6. [Supported Boards](#supported-boards)
7. [Pin Configuration](#pin-configuration)
8. [Component Programming](#component-programming)
9. [Communication Protocols](#communication-protocols)
10. [Example Projects](#example-projects)
11. [Troubleshooting](#troubleshooting)

---

## Introduction

Welcome to the Arduverse Programming Guide! This comprehensive tutorial will teach you everything you need to know to program Arduino and ESP32 boards for use with the Arduverse IoT controller app. Whether you're a complete beginner or an experienced developer, this guide will help you create amazing IoT projects.

### What You'll Learn
- How to set up your development environment
- Basic to advanced programming concepts
- How to work with sensors and actuators
- Communication protocols (WiFi, Bluetooth)
- Real-world project examples
- Best practices and troubleshooting

---

## Getting Started

### Prerequisites
- A computer (Windows, macOS, or Linux)
- An Arduino or ESP32 board
- USB cable for programming
- Basic understanding of electronics (helpful but not required)

### Required Software
1. **Arduino IDE** (recommended) or **PlatformIO**
2. **Board drivers** (usually auto-installed)
3. **Arduverse mobile app** for testing

---

## Hardware Setup

### Supported Boards

#### 1. Arduino Uno R3
- **Microcontroller**: ATmega328P
- **Operating Voltage**: 5V
- **Digital I/O Pins**: 14 (6 PWM)
- **Analog Input Pins**: 6
- **Flash Memory**: 32KB
- **SRAM**: 2KB
- **EEPROM**: 1KB
- **Clock Speed**: 16MHz

#### 2. Arduino Nano
- **Microcontroller**: ATmega328P
- **Operating Voltage**: 5V
- **Digital I/O Pins**: 14 (6 PWM)
- **Analog Input Pins**: 8
- **Flash Memory**: 32KB
- **SRAM**: 2KB
- **EEPROM**: 1KB
- **Clock Speed**: 16MHz

#### 3. Arduino Mega 2560
- **Microcontroller**: ATmega2560
- **Operating Voltage**: 5V
- **Digital I/O Pins**: 54 (15 PWM)
- **Analog Input Pins**: 16
- **Flash Memory**: 256KB
- **SRAM**: 8KB
- **EEPROM**: 4KB
- **Clock Speed**: 16MHz

#### 4. ESP32 Development Board
- **Microcontroller**: ESP32
- **Operating Voltage**: 3.3V
- **Digital I/O Pins**: 34
- **Analog Input Pins**: 18
- **Flash Memory**: 4MB
- **SRAM**: 520KB
- **WiFi**: 802.11 b/g/n
- **Bluetooth**: v4.2 BR/EDR and BLE
- **Clock Speed**: 240MHz (dual-core)

#### 5. ESP8266 (NodeMCU)
- **Microcontroller**: ESP8266
- **Operating Voltage**: 3.3V
- **Digital I/O Pins**: 17
- **Analog Input Pins**: 1
- **Flash Memory**: 4MB
- **SRAM**: 160KB
- **WiFi**: 802.11 b/g/n
- **Clock Speed**: 80MHz

---

## Arduino IDE Setup

### Step 1: Download and Install Arduino IDE
1. Visit [arduino.cc/downloads](https://arduino.cc/downloads)
2. Download the latest version for your operating system
3. Install following the setup wizard

### Step 2: Install Board Packages

#### For ESP32:
1. Open Arduino IDE
2. Go to **File > Preferences**
3. Add this URL to "Additional Board Manager URLs":
   ```
   https://dl.espressif.com/dl/package_esp32_index.json
   ```
4. Go to **Tools > Board > Boards Manager**
5. Search for "ESP32" and install "ESP32 by Espressif Systems"

#### For ESP8266:
1. Add this URL to "Additional Board Manager URLs":
   ```
   http://arduino.esp8266.com/stable/package_esp8266com_index.json
   ```
2. Search for "ESP8266" and install "ESP8266 by ESP8266 Community"

### Step 3: Install Required Libraries
Go to **Tools > Manage Libraries** and install:
- **WiFi** (for ESP32/ESP8266)
- **BluetoothSerial** (for ESP32)
- **ArduinoJson** (for data handling)
- **DHT sensor library** (for temperature sensors)
- **Servo** (for servo motors)

---

## Basic Programming Concepts

### Arduino Programming Structure
Every Arduino program (sketch) has two main functions:

```cpp
void setup() {
  // This function runs once when the board starts
  // Initialize pins, serial communication, etc.
}

void loop() {
  // This function runs repeatedly
  // Main program logic goes here
}
```

### Data Types
```cpp
int number = 42;           // Integer (-32,768 to 32,767)
float temperature = 25.5;  // Floating point number
bool isOn = true;          // Boolean (true/false)
char letter = 'A';         // Single character
String text = "Hello";     // Text string
```

### Pin Operations
```cpp
// Digital pins
pinMode(13, OUTPUT);       // Set pin 13 as output
digitalWrite(13, HIGH);    // Turn pin 13 ON (5V)
digitalWrite(13, LOW);     // Turn pin 13 OFF (0V)

int buttonState = digitalRead(2);  // Read digital pin 2

// Analog pins
int sensorValue = analogRead(A0);  // Read analog pin A0 (0-1023)
analogWrite(9, 128);              // PWM output (0-255)
```

### Serial Communication
```cpp
void setup() {
  Serial.begin(9600);  // Start serial communication at 9600 baud
}

void loop() {
  Serial.println("Hello World!");  // Print text
  Serial.print("Sensor value: ");
  Serial.println(analogRead(A0));
  delay(1000);  // Wait 1 second
}
```

---

## Pin Configuration

### Arduino Uno/Nano Pin Layout
```
Digital Pins: 0-13 (0,1 used for Serial)
PWM Pins: 3, 5, 6, 9, 10, 11
Analog Pins: A0-A5 (A6,A7 on Nano)
Power Pins: 3.3V, 5V, GND
Special Pins:
  - Pin 13: Built-in LED
  - Pin 0,1: Serial (RX,TX)
```

### Arduino Mega Pin Layout
```
Digital Pins: 0-53 (0,1 used for Serial)
PWM Pins: 2-13, 44-46
Analog Pins: A0-A15
Power Pins: 3.3V, 5V, GND
Special Pins:
  - Pin 13: Built-in LED
  - Pin 0,1: Serial (RX,TX)
  - Pin 18,19: Serial1 (RX1,TX1)
  - Pin 16,17: Serial2 (RX2,TX2)
  - Pin 14,15: Serial3 (RX3,TX3)
```

### ESP32 Pin Layout
```
Digital Pins: 0-39 (some input-only)
PWM Pins: Most digital pins support PWM
Analog Pins: 0,2,4,12-15,25-27,32-39
Touch Pins: 0,2,4,12-15,27,32,33
Power Pins: 3.3V, 5V, GND
Special Pins:
  - Pin 2: Built-in LED
  - Pin 0: Boot button
  - Pin 6-11: Connected to flash (don't use)
```

### ESP8266 (NodeMCU) Pin Layout
```
Digital Pins: D0-D8 (GPIO mapping)
PWM Pins: D1-D8
Analog Pin: A0
Power Pins: 3.3V, 5V, GND
Special Pins:
  - D0: Built-in LED (inverted)
  - D4: Built-in LED
```

---

## Component Programming

### 1. LED Control

#### Basic LED Blink
```cpp
void setup() {
  pinMode(13, OUTPUT);  // Set pin 13 as output
}

void loop() {
  digitalWrite(13, HIGH);  // Turn LED on
  delay(1000);            // Wait 1 second
  digitalWrite(13, LOW);   // Turn LED off
  delay(1000);            // Wait 1 second
}
```

#### PWM LED Dimming
```cpp
int ledPin = 9;
int brightness = 0;
int fadeAmount = 5;

void setup() {
  pinMode(ledPin, OUTPUT);
}

void loop() {
  analogWrite(ledPin, brightness);
  brightness = brightness + fadeAmount;

  if (brightness <= 0 || brightness >= 255) {
    fadeAmount = -fadeAmount;
  }
  delay(30);
}
```

### 2. Button Input

#### Basic Button Reading
```cpp
int buttonPin = 2;
int ledPin = 13;
int buttonState = 0;

void setup() {
  pinMode(ledPin, OUTPUT);
  pinMode(buttonPin, INPUT_PULLUP);  // Use internal pullup resistor
}

void loop() {
  buttonState = digitalRead(buttonPin);

  if (buttonState == LOW) {  // Button pressed (pullup inverts logic)
    digitalWrite(ledPin, HIGH);
  } else {
    digitalWrite(ledPin, LOW);
  }
}
```

#### Button with Debouncing
```cpp
int buttonPin = 2;
int ledPin = 13;
int ledState = LOW;
int buttonState;
int lastButtonState = HIGH;
unsigned long lastDebounceTime = 0;
unsigned long debounceDelay = 50;

void setup() {
  pinMode(buttonPin, INPUT_PULLUP);
  pinMode(ledPin, OUTPUT);
  digitalWrite(ledPin, ledState);
}

void loop() {
  int reading = digitalRead(buttonPin);

  if (reading != lastButtonState) {
    lastDebounceTime = millis();
  }

  if ((millis() - lastDebounceTime) > debounceDelay) {
    if (reading != buttonState) {
      buttonState = reading;

      if (buttonState == LOW) {
        ledState = !ledState;
      }
    }
  }

  digitalWrite(ledPin, ledState);
  lastButtonState = reading;
}
```

### 3. Servo Motor Control

```cpp
#include <Servo.h>

Servo myServo;
int servoPin = 9;
int potPin = A0;
int potValue;
int angle;

void setup() {
  myServo.attach(servoPin);
}

void loop() {
  potValue = analogRead(potPin);
  angle = map(potValue, 0, 1023, 0, 180);  // Map pot value to servo angle
  myServo.write(angle);
  delay(15);
}
```

### 4. DHT11/DHT22 Temperature & Humidity Sensor

```cpp
#include <DHT.h>

#define DHTPIN 2
#define DHTTYPE DHT11  // or DHT22

DHT dht(DHTPIN, DHTTYPE);

void setup() {
  Serial.begin(9600);
  dht.begin();
}

void loop() {
  float humidity = dht.readHumidity();
  float temperature = dht.readTemperature();

  if (isnan(humidity) || isnan(temperature)) {
    Serial.println("Failed to read from DHT sensor!");
    return;
  }

  Serial.print("Humidity: ");
  Serial.print(humidity);
  Serial.print("%  Temperature: ");
  Serial.print(temperature);
  Serial.println("°C");

  delay(2000);
}
```

### 5. Ultrasonic Distance Sensor (HC-SR04)

```cpp
int trigPin = 9;
int echoPin = 10;
long duration;
int distance;

void setup() {
  pinMode(trigPin, OUTPUT);
  pinMode(echoPin, INPUT);
  Serial.begin(9600);
}

void loop() {
  // Clear the trigPin
  digitalWrite(trigPin, LOW);
  delayMicroseconds(2);

  // Send 10 microsecond pulse
  digitalWrite(trigPin, HIGH);
  delayMicroseconds(10);
  digitalWrite(trigPin, LOW);

  // Read the echoPin
  duration = pulseIn(echoPin, HIGH);

  // Calculate distance in cm
  distance = duration * 0.034 / 2;

  Serial.print("Distance: ");
  Serial.print(distance);
  Serial.println(" cm");

  delay(1000);
}
```

### 6. PIR Motion Sensor

```cpp
int pirPin = 2;
int ledPin = 13;
int pirState = LOW;
int val = 0;

void setup() {
  pinMode(ledPin, OUTPUT);
  pinMode(pirPin, INPUT);
  Serial.begin(9600);
}

void loop() {
  val = digitalRead(pirPin);

  if (val == HIGH) {
    digitalWrite(ledPin, HIGH);
    if (pirState == LOW) {
      Serial.println("Motion detected!");
      pirState = HIGH;
    }
  } else {
    digitalWrite(ledPin, LOW);
    if (pirState == HIGH) {
      Serial.println("Motion ended!");
      pirState = LOW;
    }
  }
}
```

### 7. LDR (Light Dependent Resistor)

```cpp
int ldrPin = A0;
int ledPin = 9;
int ldrValue = 0;
int ledBrightness = 0;

void setup() {
  pinMode(ledPin, OUTPUT);
  Serial.begin(9600);
}

void loop() {
  ldrValue = analogRead(ldrPin);

  // Invert the reading (darker = brighter LED)
  ledBrightness = map(ldrValue, 0, 1023, 255, 0);

  analogWrite(ledPin, ledBrightness);

  Serial.print("LDR Value: ");
  Serial.print(ldrValue);
  Serial.print("  LED Brightness: ");
  Serial.println(ledBrightness);

  delay(100);
}
```

---

## Communication Protocols

### 1. WiFi Communication (ESP32/ESP8266)

#### Basic WiFi Connection
```cpp
#include <WiFi.h>  // For ESP32
// #include <ESP8266WiFi.h>  // For ESP8266

const char* ssid = "your_wifi_name";
const char* password = "your_wifi_password";

void setup() {
  Serial.begin(115200);

  WiFi.begin(ssid, password);

  while (WiFi.status() != WL_CONNECTED) {
    delay(1000);
    Serial.println("Connecting to WiFi...");
  }

  Serial.println("Connected to WiFi!");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());
}

void loop() {
  // Your main code here
}
```

#### WiFi Web Server
```cpp
#include <WiFi.h>
#include <WebServer.h>

const char* ssid = "your_wifi_name";
const char* password = "your_wifi_password";

WebServer server(80);
int ledPin = 2;

void setup() {
  Serial.begin(115200);
  pinMode(ledPin, OUTPUT);

  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(1000);
    Serial.println("Connecting to WiFi...");
  }

  Serial.println("WiFi connected!");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());

  server.on("/", handleRoot);
  server.on("/led/on", handleLedOn);
  server.on("/led/off", handleLedOff);

  server.begin();
  Serial.println("HTTP server started");
}

void loop() {
  server.handleClient();
}

void handleRoot() {
  String html = "<html><body>";
  html += "<h1>ESP32 Web Server</h1>";
  html += "<p><a href=\"/led/on\">Turn LED ON</a></p>";
  html += "<p><a href=\"/led/off\">Turn LED OFF</a></p>";
  html += "</body></html>";

  server.send(200, "text/html", html);
}

void handleLedOn() {
  digitalWrite(ledPin, HIGH);
  server.send(200, "text/plain", "LED is ON");
}

void handleLedOff() {
  digitalWrite(ledPin, LOW);
  server.send(200, "text/plain", "LED is OFF");
}
```

### 2. Bluetooth Communication (ESP32)

```cpp
#include "BluetoothSerial.h"

BluetoothSerial SerialBT;
int ledPin = 2;

void setup() {
  Serial.begin(115200);
  SerialBT.begin("ESP32test"); // Bluetooth device name
  Serial.println("The device started, now you can pair it with bluetooth!");
  pinMode(ledPin, OUTPUT);
}

void loop() {
  if (SerialBT.available()) {
    String message = SerialBT.readString();
    message.trim();

    Serial.println("Received: " + message);

    if (message == "ON") {
      digitalWrite(ledPin, HIGH);
      SerialBT.println("LED turned ON");
    }
    else if (message == "OFF") {
      digitalWrite(ledPin, LOW);
      SerialBT.println("LED turned OFF");
    }
    else {
      SerialBT.println("Unknown command");
    }
  }

  delay(20);
}
```

---

## Example Projects

### Project 1: Smart Home Lighting System

This project creates a smart lighting system that can be controlled via WiFi and responds to ambient light levels.

```cpp
#include <WiFi.h>
#include <WebServer.h>

// WiFi credentials
const char* ssid = "your_wifi_name";
const char* password = "your_wifi_password";

// Pin definitions
const int ledPin = 2;
const int ldrPin = A0;
const int buttonPin = 4;

// Variables
WebServer server(80);
bool autoMode = true;
bool ledState = false;
int lightThreshold = 500;

void setup() {
  Serial.begin(115200);

  // Initialize pins
  pinMode(ledPin, OUTPUT);
  pinMode(buttonPin, INPUT_PULLUP);

  // Connect to WiFi
  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(1000);
    Serial.println("Connecting to WiFi...");
  }

  Serial.println("WiFi connected!");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());

  // Setup web server routes
  server.on("/", handleRoot);
  server.on("/toggle", handleToggle);
  server.on("/auto", handleAuto);
  server.on("/manual", handleManual);
  server.on("/status", handleStatus);

  server.begin();
  Serial.println("HTTP server started");
}

void loop() {
  server.handleClient();

  // Check button press
  if (digitalRead(buttonPin) == LOW) {
    delay(50); // Debounce
    if (digitalRead(buttonPin) == LOW) {
      toggleLed();
      while (digitalRead(buttonPin) == LOW); // Wait for release
    }
  }

  // Auto mode logic
  if (autoMode) {
    int ldrValue = analogRead(ldrPin);
    if (ldrValue < lightThreshold && !ledState) {
      ledState = true;
      digitalWrite(ledPin, HIGH);
      Serial.println("Auto: LED ON (dark detected)");
    } else if (ldrValue >= lightThreshold && ledState) {
      ledState = false;
      digitalWrite(ledPin, LOW);
      Serial.println("Auto: LED OFF (light detected)");
    }
  }

  delay(100);
}

void handleRoot() {
  String html = "<html><head><title>Smart Lighting</title></head><body>";
  html += "<h1>Smart Home Lighting System</h1>";
  html += "<p>LED Status: " + String(ledState ? "ON" : "OFF") + "</p>";
  html += "<p>Mode: " + String(autoMode ? "AUTO" : "MANUAL") + "</p>";
  html += "<p>Light Level: " + String(analogRead(ldrPin)) + "</p>";
  html += "<p><a href=\"/toggle\">Toggle LED</a></p>";
  html += "<p><a href=\"/auto\">Auto Mode</a></p>";
  html += "<p><a href=\"/manual\">Manual Mode</a></p>";
  html += "<script>setTimeout(function(){location.reload()}, 2000);</script>";
  html += "</body></html>";

  server.send(200, "text/html", html);
}

void handleToggle() {
  toggleLed();
  server.send(200, "text/plain", "LED toggled");
}

void handleAuto() {
  autoMode = true;
  server.send(200, "text/plain", "Auto mode enabled");
}

void handleManual() {
  autoMode = false;
  server.send(200, "text/plain", "Manual mode enabled");
}

void handleStatus() {
  String json = "{";
  json += "\"led\":" + String(ledState ? "true" : "false") + ",";
  json += "\"mode\":\"" + String(autoMode ? "auto" : "manual") + "\",";
  json += "\"light\":" + String(analogRead(ldrPin));
  json += "}";

  server.send(200, "application/json", json);
}

void toggleLed() {
  ledState = !ledState;
  digitalWrite(ledPin, ledState);
  Serial.println("LED " + String(ledState ? "ON" : "OFF"));
}
```

### Project 2: Environmental Monitoring Station

This project creates a comprehensive environmental monitoring system with multiple sensors.

```cpp
#include <WiFi.h>
#include <WebServer.h>
#include <DHT.h>

// WiFi credentials
const char* ssid = "your_wifi_name";
const char* password = "your_wifi_password";

// Pin definitions
#define DHTPIN 4
#define DHTTYPE DHT22
const int ldrPin = A0;
const int pirPin = 2;
const int buzzerPin = 5;

// Sensor objects
DHT dht(DHTPIN, DHTTYPE);
WebServer server(80);

// Variables
float temperature = 0;
float humidity = 0;
int lightLevel = 0;
bool motionDetected = false;
unsigned long lastSensorRead = 0;
const unsigned long sensorInterval = 2000; // Read sensors every 2 seconds

void setup() {
  Serial.begin(115200);

  // Initialize pins
  pinMode(pirPin, INPUT);
  pinMode(buzzerPin, OUTPUT);

  // Initialize sensors
  dht.begin();

  // Connect to WiFi
  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(1000);
    Serial.println("Connecting to WiFi...");
  }

  Serial.println("WiFi connected!");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());

  // Setup web server routes
  server.on("/", handleRoot);
  server.on("/data", handleData);
  server.on("/alarm", handleAlarm);

  server.begin();
  Serial.println("Environmental monitoring server started");
}

void loop() {
  server.handleClient();

  // Read sensors periodically
  if (millis() - lastSensorRead >= sensorInterval) {
    readSensors();
    lastSensorRead = millis();
  }

  // Check for motion
  if (digitalRead(pirPin) == HIGH) {
    if (!motionDetected) {
      motionDetected = true;
      Serial.println("Motion detected!");
      // Optional: Sound alarm
      // soundAlarm();
    }
  } else {
    motionDetected = false;
  }

  delay(100);
}

void readSensors() {
  // Read DHT sensor
  float newTemp = dht.readTemperature();
  float newHum = dht.readHumidity();

  if (!isnan(newTemp) && !isnan(newHum)) {
    temperature = newTemp;
    humidity = newHum;
  }

  // Read light sensor
  lightLevel = analogRead(ldrPin);

  // Print to serial
  Serial.println("=== Sensor Readings ===");
  Serial.println("Temperature: " + String(temperature) + "°C");
  Serial.println("Humidity: " + String(humidity) + "%");
  Serial.println("Light Level: " + String(lightLevel));
  Serial.println("Motion: " + String(motionDetected ? "YES" : "NO"));
  Serial.println("=======================");
}

void handleRoot() {
  String html = "<!DOCTYPE html><html><head>";
  html += "<title>Environmental Monitor</title>";
  html += "<meta http-equiv='refresh' content='5'>";
  html += "<style>body{font-family:Arial;margin:40px;} .sensor{background:#f0f0f0;padding:20px;margin:10px;border-radius:10px;}</style>";
  html += "</head><body>";
  html += "<h1>Environmental Monitoring Station</h1>";

  html += "<div class='sensor'>";
  html += "<h2>Temperature</h2>";
  html += "<p style='font-size:24px;color:#ff6600;'>" + String(temperature) + "°C</p>";
  html += "</div>";

  html += "<div class='sensor'>";
  html += "<h2>Humidity</h2>";
  html += "<p style='font-size:24px;color:#0066ff;'>" + String(humidity) + "%</p>";
  html += "</div>";

  html += "<div class='sensor'>";
  html += "<h2>Light Level</h2>";
  html += "<p style='font-size:24px;color:#ffcc00;'>" + String(lightLevel) + "</p>";
  html += "</div>";

  html += "<div class='sensor'>";
  html += "<h2>Motion Detection</h2>";
  html += "<p style='font-size:24px;color:" + String(motionDetected ? "#ff0000" : "#00ff00") + ";'>";
  html += motionDetected ? "MOTION DETECTED" : "NO MOTION";
  html += "</p>";
  html += "</div>";

  html += "<p><a href='/data'>Get JSON Data</a></p>";
  html += "</body></html>";

  server.send(200, "text/html", html);
}

void handleData() {
  String json = "{";
  json += "\"temperature\":" + String(temperature) + ",";
  json += "\"humidity\":" + String(humidity) + ",";
  json += "\"light\":" + String(lightLevel) + ",";
  json += "\"motion\":" + String(motionDetected ? "true" : "false") + ",";
  json += "\"timestamp\":" + String(millis());
  json += "}";

  server.send(200, "application/json", json);
}

void handleAlarm() {
  soundAlarm();
  server.send(200, "text/plain", "Alarm activated");
}

void soundAlarm() {
  for (int i = 0; i < 3; i++) {
    digitalWrite(buzzerPin, HIGH);
    delay(200);
    digitalWrite(buzzerPin, LOW);
    delay(200);
  }
}
```

### Project 3: Bluetooth Controlled Robot Car

This project creates a simple robot car controlled via Bluetooth from your smartphone.

```cpp
#include "BluetoothSerial.h"

BluetoothSerial SerialBT;

// Motor pins
const int motor1Pin1 = 2;
const int motor1Pin2 = 4;
const int motor2Pin1 = 5;
const int motor2Pin2 = 18;
const int enable1Pin = 19;
const int enable2Pin = 21;

// LED pins
const int frontLED = 23;
const int backLED = 22;

// Variables
int speed = 200; // PWM speed (0-255)
bool lightsOn = false;

void setup() {
  Serial.begin(115200);
  SerialBT.begin("RobotCar"); // Bluetooth device name
  Serial.println("Robot car ready for pairing!");

  // Initialize motor pins
  pinMode(motor1Pin1, OUTPUT);
  pinMode(motor1Pin2, OUTPUT);
  pinMode(motor2Pin1, OUTPUT);
  pinMode(motor2Pin2, OUTPUT);
  pinMode(enable1Pin, OUTPUT);
  pinMode(enable2Pin, OUTPUT);

  // Initialize LED pins
  pinMode(frontLED, OUTPUT);
  pinMode(backLED, OUTPUT);

  // Set initial speed
  analogWrite(enable1Pin, speed);
  analogWrite(enable2Pin, speed);

  stopMotors();
}

void loop() {
  if (SerialBT.available()) {
    String command = SerialBT.readString();
    command.trim();

    Serial.println("Received: " + command);

    if (command == "FORWARD" || command == "F") {
      moveForward();
      SerialBT.println("Moving forward");
    }
    else if (command == "BACKWARD" || command == "B") {
      moveBackward();
      SerialBT.println("Moving backward");
    }
    else if (command == "LEFT" || command == "L") {
      turnLeft();
      SerialBT.println("Turning left");
    }
    else if (command == "RIGHT" || command == "R") {
      turnRight();
      SerialBT.println("Turning right");
    }
    else if (command == "STOP" || command == "S") {
      stopMotors();
      SerialBT.println("Stopped");
    }
    else if (command == "LIGHTS") {
      toggleLights();
      SerialBT.println("Lights " + String(lightsOn ? "ON" : "OFF"));
    }
    else if (command.startsWith("SPEED")) {
      int newSpeed = command.substring(5).toInt();
      if (newSpeed >= 0 && newSpeed <= 255) {
        speed = newSpeed;
        analogWrite(enable1Pin, speed);
        analogWrite(enable2Pin, speed);
        SerialBT.println("Speed set to " + String(speed));
      }
    }
    else {
      SerialBT.println("Unknown command. Use: FORWARD, BACKWARD, LEFT, RIGHT, STOP, LIGHTS, SPEED###");
    }
  }

  delay(20);
}

void moveForward() {
  digitalWrite(motor1Pin1, HIGH);
  digitalWrite(motor1Pin2, LOW);
  digitalWrite(motor2Pin1, HIGH);
  digitalWrite(motor2Pin2, LOW);
}

void moveBackward() {
  digitalWrite(motor1Pin1, LOW);
  digitalWrite(motor1Pin2, HIGH);
  digitalWrite(motor2Pin1, LOW);
  digitalWrite(motor2Pin2, HIGH);

  // Turn on back LED when reversing
  digitalWrite(backLED, HIGH);
  delay(100);
  digitalWrite(backLED, LOW);
}

void turnLeft() {
  digitalWrite(motor1Pin1, LOW);
  digitalWrite(motor1Pin2, HIGH);
  digitalWrite(motor2Pin1, HIGH);
  digitalWrite(motor2Pin2, LOW);
}

void turnRight() {
  digitalWrite(motor1Pin1, HIGH);
  digitalWrite(motor1Pin2, LOW);
  digitalWrite(motor2Pin1, LOW);
  digitalWrite(motor2Pin2, HIGH);
}

void stopMotors() {
  digitalWrite(motor1Pin1, LOW);
  digitalWrite(motor1Pin2, LOW);
  digitalWrite(motor2Pin1, LOW);
  digitalWrite(motor2Pin2, LOW);
}

void toggleLights() {
  lightsOn = !lightsOn;
  digitalWrite(frontLED, lightsOn);
}
```

---

## Troubleshooting

### Common Issues and Solutions

#### 1. Board Not Detected
**Problem**: Arduino IDE doesn't recognize your board
**Solutions**:
- Check USB cable (try a different one)
- Install board drivers
- Try a different USB port
- Restart Arduino IDE
- Check if board is in bootloader mode (for ESP32/ESP8266)

#### 2. Upload Failed
**Problem**: Code won't upload to board
**Solutions**:
- Select correct board and port in Tools menu
- Close Serial Monitor before uploading
- Press and hold BOOT button during upload (ESP32/ESP8266)
- Check for syntax errors in code
- Reduce code size if memory is full

#### 3. WiFi Connection Issues
**Problem**: ESP32/ESP8266 won't connect to WiFi
**Solutions**:
- Double-check SSID and password
- Ensure WiFi is 2.4GHz (not 5GHz)
- Check signal strength
- Try connecting to a mobile hotspot
- Add delays in connection code

#### 4. Sensor Reading Errors
**Problem**: Sensors return incorrect or NaN values
**Solutions**:
- Check wiring connections
- Verify power supply (3.3V vs 5V)
- Add pull-up resistors if needed
- Check sensor datasheet for timing requirements
- Add error handling in code

#### 5. Memory Issues
**Problem**: "Not enough memory" or random crashes
**Solutions**:
- Use `const` for strings stored in flash memory
- Avoid large arrays in RAM
- Use `F()` macro for string literals
- Free unused variables
- Consider using external memory

### Best Practices

#### Code Organization
```cpp
// Use meaningful variable names
int temperatureSensor = A0;  // Good
int a = A0;                  // Bad

// Add comments
// Read temperature every 5 seconds
if (millis() - lastReading > 5000) {
  temperature = readTemperature();
  lastReading = millis();
}

// Use constants for pin definitions
const int LED_PIN = 13;
const int BUTTON_PIN = 2;
```

#### Power Management
```cpp
// For battery-powered projects, use deep sleep
#include <esp_sleep.h>

void goToSleep() {
  esp_sleep_enable_timer_wakeup(60 * 1000000); // 60 seconds
  esp_deep_sleep_start();
}
```

#### Error Handling
```cpp
// Always check sensor readings
float temp = dht.readTemperature();
if (isnan(temp)) {
  Serial.println("Error reading temperature!");
  return; // Exit function or handle error
}
```

### Debugging Tips

1. **Use Serial Monitor**: Always print debug information
2. **Check Connections**: Use a multimeter to verify wiring
3. **Test Components**: Test each component separately
4. **Start Simple**: Begin with basic examples and add complexity
5. **Read Documentation**: Check component datasheets and library documentation

---

## Advanced Topics

### 1. Interrupts
```cpp
volatile bool buttonPressed = false;

void setup() {
  pinMode(2, INPUT_PULLUP);
  attachInterrupt(digitalPinToInterrupt(2), buttonISR, FALLING);
}

void loop() {
  if (buttonPressed) {
    Serial.println("Button was pressed!");
    buttonPressed = false;
  }
}

void buttonISR() {
  buttonPressed = true;
}
```

### 2. Timers and Scheduling
```cpp
unsigned long previousMillis = 0;
const long interval = 1000;

void loop() {
  unsigned long currentMillis = millis();

  if (currentMillis - previousMillis >= interval) {
    previousMillis = currentMillis;
    // Code to run every second
    Serial.println("Timer tick");
  }
}
```

### 3. EEPROM Storage
```cpp
#include <EEPROM.h>

void setup() {
  EEPROM.begin(512); // For ESP32/ESP8266

  // Write data
  EEPROM.write(0, 42);
  EEPROM.commit(); // For ESP32/ESP8266

  // Read data
  int value = EEPROM.read(0);
  Serial.println(value);
}
```

---

## Conclusion

This comprehensive guide covers everything you need to start programming Arduino and ESP32 boards for IoT projects. Remember to:

1. Start with simple projects and gradually increase complexity
2. Always test your circuits before powering on
3. Use proper error handling in your code
4. Document your projects for future reference
5. Join the Arduino community for support and inspiration

### Additional Resources

- **Arduino Official Documentation**: [arduino.cc/reference](https://arduino.cc/reference)
- **ESP32 Documentation**: [docs.espressif.com](https://docs.espressif.com)
- **Arduino Forum**: [forum.arduino.cc](https://forum.arduino.cc)
- **GitHub Arduino Libraries**: [github.com/arduino-libraries](https://github.com/arduino-libraries)

Happy coding with Arduverse! 🚀