import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/automation.dart';
import '../services/storage_service.dart';

class AutomationNotifier extends StateNotifier<List<Automation>> {
  AutomationNotifier(this._storageService) : super([]) {
    _loadAutomations();
  }

  final StorageService _storageService;
  static const _uuid = Uuid();

  Future<void> _loadAutomations() async {
    try {
      final automations = await _storageService.getAutomations();
      state = automations;
    } catch (e) {
      state = [];
    }
  }

  Future<void> addAutomation({
    required String name,
    required String roomId,
    required AutomationCondition condition,
    required AutomationAction action,
    String? description,
  }) async {
    if (name.trim().isEmpty) {
      throw ArgumentError('Automation name cannot be empty');
    }

    if (state.any((automation) => automation.name.toLowerCase() == name.toLowerCase())) {
      throw ArgumentError('An automation with this name already exists');
    }

    final now = DateTime.now();
    final newAutomation = Automation(
      id: _uuid.v4(),
      name: name.trim(),
      roomId: roomId,
      condition: condition,
      action: action,
      description: description,
      createdAt: now,
      updatedAt: now,
    );

    state = [...state, newAutomation];
    await _storageService.saveAutomation(newAutomation);
  }

  Future<void> updateAutomation(Automation updatedAutomation) async {
    final index = state.indexWhere((automation) => automation.id == updatedAutomation.id);
    if (index == -1) {
      throw ArgumentError('Automation not found');
    }

    final newState = [...state];
    newState[index] = updatedAutomation.copyWith(updatedAt: DateTime.now());
    state = newState;
    
    await _storageService.saveAutomation(newState[index]);
  }

  Future<void> deleteAutomation(String automationId) async {
    final automation = state.firstWhere(
      (automation) => automation.id == automationId,
      orElse: () => throw ArgumentError('Automation not found'),
    );

    state = state.where((automation) => automation.id != automationId).toList();
    await _storageService.deleteAutomation(automationId);
  }

  Future<void> toggleAutomationEnabled(String automationId) async {
    final automation = getAutomationById(automationId);
    if (automation == null) return;

    await updateAutomation(automation.copyWith(isEnabled: !automation.isEnabled));
  }

  Future<void> executeAutomation(String automationId) async {
    final automation = getAutomationById(automationId);
    if (automation == null || !automation.isEnabled) return;

    // Increment execution count
    final updatedAutomation = automation.incrementExecutionCount();
    await updateAutomation(updatedAutomation);

    // Here you would implement the actual automation execution logic
    // This would involve sending commands to the target component
  }

  Future<void> evaluateAutomations(String componentId, double currentValue) async {
    final relevantAutomations = state.where((automation) => 
        automation.isEnabled && 
        automation.condition.componentId == componentId).toList();

    for (final automation in relevantAutomations) {
      if (automation.evaluateCondition(currentValue)) {
        await executeAutomation(automation.id);
      }
    }
  }

  Automation? getAutomationById(String automationId) {
    try {
      return state.firstWhere((automation) => automation.id == automationId);
    } catch (e) {
      return null;
    }
  }

  List<Automation> getAutomationsByRoom(String roomId) {
    return state.where((automation) => automation.roomId == roomId).toList();
  }

  List<Automation> getEnabledAutomations() {
    return state.where((automation) => automation.isEnabled).toList();
  }

  List<Automation> getAutomationsByComponent(String componentId) {
    return state.where((automation) => 
        automation.condition.componentId == componentId ||
        automation.action.componentId == componentId).toList();
  }
}

final automationProvider = StateNotifierProvider<AutomationNotifier, List<Automation>>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return AutomationNotifier(storageService);
});

// Convenience providers
final enabledAutomationsProvider = Provider<List<Automation>>((ref) {
  final automations = ref.watch(automationProvider);
  return automations.where((automation) => automation.isEnabled).toList();
});

final disabledAutomationsProvider = Provider<List<Automation>>((ref) {
  final automations = ref.watch(automationProvider);
  return automations.where((automation) => !automation.isEnabled).toList();
});

// Provider for getting automations by room
final automationsByRoomProvider = Provider.family<List<Automation>, String>((ref, roomId) {
  final automations = ref.watch(automationProvider);
  return automations.where((automation) => automation.roomId == roomId).toList();
});

// Provider for getting automations by component
final automationsByComponentProvider = Provider.family<List<Automation>, String>((ref, componentId) {
  final automations = ref.watch(automationProvider);
  return automations.where((automation) => 
      automation.condition.componentId == componentId ||
      automation.action.componentId == componentId).toList();
});

// Provider for getting a specific automation by ID
final automationByIdProvider = Provider.family<Automation?, String>((ref, automationId) {
  final automations = ref.watch(automationProvider);
  try {
    return automations.firstWhere((automation) => automation.id == automationId);
  } catch (e) {
    return null;
  }
});

// Provider for checking if any automations exist
final hasAutomationsProvider = Provider<bool>((ref) {
  final automations = ref.watch(automationProvider);
  return automations.isNotEmpty;
});
