// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'automation.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AutomationAdapter extends TypeAdapter<Automation> {
  @override
  final int typeId = 6;

  @override
  Automation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Automation(
      id: fields[0] as String,
      name: fields[1] as String,
      roomId: fields[2] as String,
      condition: fields[3] as AutomationCondition,
      action: fields[4] as AutomationAction,
      isEnabled: fields[5] as bool,
      createdAt: fields[6] as DateTime,
      updatedAt: fields[7] as DateTime,
      description: fields[8] as String?,
      executionCount: fields[9] as int,
      lastExecuted: fields[10] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, Automation obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.roomId)
      ..writeByte(3)
      ..write(obj.condition)
      ..writeByte(4)
      ..write(obj.action)
      ..writeByte(5)
      ..write(obj.isEnabled)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.updatedAt)
      ..writeByte(8)
      ..write(obj.description)
      ..writeByte(9)
      ..write(obj.executionCount)
      ..writeByte(10)
      ..write(obj.lastExecuted);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AutomationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AutomationConditionAdapter extends TypeAdapter<AutomationCondition> {
  @override
  final int typeId = 7;

  @override
  AutomationCondition read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AutomationCondition(
      componentId: fields[0] as String,
      comparator: fields[1] as Comparator,
      value: fields[2] as double,
    );
  }

  @override
  void write(BinaryWriter writer, AutomationCondition obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.componentId)
      ..writeByte(1)
      ..write(obj.comparator)
      ..writeByte(2)
      ..write(obj.value);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AutomationConditionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AutomationActionAdapter extends TypeAdapter<AutomationAction> {
  @override
  final int typeId = 8;

  @override
  AutomationAction read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AutomationAction(
      componentId: fields[0] as String,
      value: fields[1] as dynamic,
    );
  }

  @override
  void write(BinaryWriter writer, AutomationAction obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.componentId)
      ..writeByte(1)
      ..write(obj.value);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AutomationActionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ComparatorAdapter extends TypeAdapter<Comparator> {
  @override
  final int typeId = 9;

  @override
  Comparator read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return Comparator.equals;
      case 1:
        return Comparator.greaterThan;
      case 2:
        return Comparator.lessThan;
      case 3:
        return Comparator.greaterThanOrEqual;
      case 4:
        return Comparator.lessThanOrEqual;
      default:
        return Comparator.equals;
    }
  }

  @override
  void write(BinaryWriter writer, Comparator obj) {
    switch (obj) {
      case Comparator.equals:
        writer.writeByte(0);
        break;
      case Comparator.greaterThan:
        writer.writeByte(1);
        break;
      case Comparator.lessThan:
        writer.writeByte(2);
        break;
      case Comparator.greaterThanOrEqual:
        writer.writeByte(3);
        break;
      case Comparator.lessThanOrEqual:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ComparatorAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Automation _$AutomationFromJson(Map<String, dynamic> json) => Automation(
      id: json['id'] as String,
      name: json['name'] as String,
      roomId: json['roomId'] as String,
      condition: AutomationCondition.fromJson(
          json['condition'] as Map<String, dynamic>),
      action: AutomationAction.fromJson(json['action'] as Map<String, dynamic>),
      isEnabled: json['isEnabled'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      description: json['description'] as String?,
      executionCount: (json['executionCount'] as num?)?.toInt() ?? 0,
      lastExecuted: json['lastExecuted'] == null
          ? null
          : DateTime.parse(json['lastExecuted'] as String),
    );

Map<String, dynamic> _$AutomationToJson(Automation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'roomId': instance.roomId,
      'condition': instance.condition,
      'action': instance.action,
      'isEnabled': instance.isEnabled,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'description': instance.description,
      'executionCount': instance.executionCount,
      'lastExecuted': instance.lastExecuted?.toIso8601String(),
    };

AutomationCondition _$AutomationConditionFromJson(Map<String, dynamic> json) =>
    AutomationCondition(
      componentId: json['componentId'] as String,
      comparator: $enumDecode(_$ComparatorEnumMap, json['comparator']),
      value: (json['value'] as num).toDouble(),
    );

Map<String, dynamic> _$AutomationConditionToJson(
        AutomationCondition instance) =>
    <String, dynamic>{
      'componentId': instance.componentId,
      'comparator': _$ComparatorEnumMap[instance.comparator]!,
      'value': instance.value,
    };

const _$ComparatorEnumMap = {
  Comparator.equals: 'equals',
  Comparator.greaterThan: 'greaterThan',
  Comparator.lessThan: 'lessThan',
  Comparator.greaterThanOrEqual: 'greaterThanOrEqual',
  Comparator.lessThanOrEqual: 'lessThanOrEqual',
};

AutomationAction _$AutomationActionFromJson(Map<String, dynamic> json) =>
    AutomationAction(
      componentId: json['componentId'] as String,
      value: json['value'],
    );

Map<String, dynamic> _$AutomationActionToJson(AutomationAction instance) =>
    <String, dynamic>{
      'componentId': instance.componentId,
      'value': instance.value,
    };
