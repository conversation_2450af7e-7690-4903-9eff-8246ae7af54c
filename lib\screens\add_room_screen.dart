import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/room.dart';
import '../providers/room_provider.dart';

class AddRoomScreen extends ConsumerStatefulWidget {
  const AddRoomScreen({super.key});

  @override
  ConsumerState<AddRoomScreen> createState() => _AddRoomScreenState();
}

class _AddRoomScreenState extends ConsumerState<AddRoomScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  String _selectedIcon = 'home';
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Room'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveRoom,
            child: _isLoading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Room Name',
                hintText: 'e.g., Living Room, Kitchen',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a room name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                hintText: 'Brief description of the room',
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 24),
            
            Text(
              'Choose Icon',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            
            _buildIconSelector(),
            
            const SizedBox(height: 32),
            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Next Steps',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'After creating this room, you can add components from your connected devices to control them from this room.',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconSelector() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: RoomIcons.available.length,
      itemBuilder: (context, index) {
        final iconName = RoomIcons.available[index];
        final isSelected = iconName == _selectedIcon;
        
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedIcon = iconName;
            });
          },
          child: Container(
            decoration: BoxDecoration(
              color: isSelected 
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : null,
              border: Border.all(
                color: isSelected 
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).dividerColor,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getIconData(iconName),
                  color: isSelected 
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).iconTheme.color,
                ),
                const SizedBox(height: 4),
                Text(
                  RoomIcons.getDisplayName(iconName),
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: isSelected 
                        ? Theme.of(context).primaryColor
                        : null,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'home':
        return MdiIcons.home;
      case 'bedroom':
        return MdiIcons.bed;
      case 'living_room':
        return MdiIcons.sofa;
      case 'kitchen':
        return MdiIcons.chefHat;
      case 'bathroom':
        return MdiIcons.shower;
      case 'garage':
        return MdiIcons.garage;
      case 'garden':
        return MdiIcons.flower;
      case 'office':
        return MdiIcons.briefcase;
      case 'workshop':
        return MdiIcons.tools;
      case 'basement':
        return MdiIcons.stairs;
      case 'attic':
        return MdiIcons.home;
      case 'balcony':
        return MdiIcons.balcony;
      case 'dining_room':
        return MdiIcons.silverwareForkKnife;
      case 'laundry':
        return MdiIcons.washingMachine;
      case 'storage':
        return MdiIcons.archive;
      case 'gym':
        return MdiIcons.dumbbell;
      case 'library':
        return MdiIcons.bookshelf;
      case 'music_room':
        return MdiIcons.music;
      case 'game_room':
        return MdiIcons.gamepad;
      case 'nursery':
        return MdiIcons.baby;
      default:
        return MdiIcons.home;
    }
  }

  Future<void> _saveRoom() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(roomProvider.notifier).addRoom(
        name: _nameController.text.trim(),
        iconName: _selectedIcon,
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Room "${_nameController.text.trim()}" created'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
