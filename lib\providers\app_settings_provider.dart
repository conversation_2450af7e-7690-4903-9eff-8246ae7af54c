import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../models/app_settings.dart';

class AppSettingsNotifier extends StateNotifier<AppSettings> {
  AppSettingsNotifier() : super(AppSettings.defaultSettings()) {
    _loadSettings();
  }

  static const String _settingsKey = 'app_settings';

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        // In a real app, you'd deserialize from JSON here
        // For now, we'll use default settings
        state = AppSettings.defaultSettings();
      }
    } catch (e) {
      // If loading fails, keep default settings
      debugPrint('Failed to load settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // In a real app, you'd serialize to JSON here
      await prefs.setString(_settingsKey, state.toJson().toString());
    } catch (e) {
      debugPrint('Failed to save settings: $e');
    }
  }

  Future<void> updateThemeMode(ThemeMode themeMode) async {
    state = state.copyWith(
      themeMode: themeMode,
      updatedAt: DateTime.now(),
    );
    await _saveSettings();
  }

  Future<void> updateDefaultWifiSettings(String ip, int port) async {
    if (!state.isValidWifiIp(ip) || !state.isValidPort(port)) {
      throw ArgumentError('Invalid WiFi settings');
    }

    state = state.copyWith(
      defaultWifiIp: ip,
      defaultWifiPort: port,
      updatedAt: DateTime.now(),
    );
    await _saveSettings();
  }

  Future<void> updateNotificationsEnabled(bool enabled) async {
    state = state.copyWith(
      notificationsEnabled: enabled,
      updatedAt: DateTime.now(),
    );
    await _saveSettings();
  }

  Future<void> updateAutoConnectDevices(bool enabled) async {
    state = state.copyWith(
      autoConnectDevices: enabled,
      updatedAt: DateTime.now(),
    );
    await _saveSettings();
  }

  Future<void> updateConnectionTimeout(int timeout) async {
    if (timeout < 5 || timeout > 60) {
      throw ArgumentError('Connection timeout must be between 5 and 60 seconds');
    }

    state = state.copyWith(
      connectionTimeout: timeout,
      updatedAt: DateTime.now(),
    );
    await _saveSettings();
  }

  Future<void> updateKeepScreenOn(bool enabled) async {
    state = state.copyWith(
      keepScreenOn: enabled,
      updatedAt: DateTime.now(),
    );
    await _saveSettings();
  }

  Future<void> updateShowDebugInfo(bool enabled) async {
    state = state.copyWith(
      showDebugInfo: enabled,
      updatedAt: DateTime.now(),
    );
    await _saveSettings();
  }

  Future<void> updateHapticFeedback(bool enabled) async {
    state = state.copyWith(
      hapticFeedback: enabled,
      updatedAt: DateTime.now(),
    );
    await _saveSettings();
  }

  Future<void> updateRefreshInterval(double interval) async {
    if (interval < 0.5 || interval > 10.0) {
      throw ArgumentError('Refresh interval must be between 0.5 and 10.0 seconds');
    }

    state = state.copyWith(
      refreshInterval: interval,
      updatedAt: DateTime.now(),
    );
    await _saveSettings();
  }

  Future<void> resetToDefaults() async {
    state = AppSettings.defaultSettings();
    await _saveSettings();
  }
}

final appSettingsProvider = StateNotifierProvider<AppSettingsNotifier, AppSettings>((ref) {
  return AppSettingsNotifier();
});

// Convenience providers for specific settings
final themeModeProvider = Provider<ThemeMode>((ref) {
  return ref.watch(appSettingsProvider).themeMode;
});

final defaultWifiSettingsProvider = Provider<({String ip, int port})>((ref) {
  final settings = ref.watch(appSettingsProvider);
  return (ip: settings.defaultWifiIp, port: settings.defaultWifiPort);
});

final connectionTimeoutProvider = Provider<int>((ref) {
  return ref.watch(appSettingsProvider).connectionTimeout;
});

final autoConnectDevicesProvider = Provider<bool>((ref) {
  return ref.watch(appSettingsProvider).autoConnectDevices;
});

final refreshIntervalProvider = Provider<double>((ref) {
  return ref.watch(appSettingsProvider).refreshInterval;
});

final hapticFeedbackProvider = Provider<bool>((ref) {
  return ref.watch(appSettingsProvider).hapticFeedback;
});

final showDebugInfoProvider = Provider<bool>((ref) {
  return ref.watch(appSettingsProvider).showDebugInfo;
});
