import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'device.g.dart';

@HiveType(typeId: 0)
@JsonSerializable()
class Device {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final DeviceType type;

  @HiveField(3)
  final ConnectionType connectionType;

  @HiveField(4)
  final String? bluetoothAddress;

  @HiveField(5)
  final String? wifiIp;

  @HiveField(6)
  final int? wifiPort;

  @HiveField(7)
  final bool isEnabled;

  @HiveField(8)
  final bool isConnected;

  @HiveField(9)
  final DateTime createdAt;

  @HiveField(10)
  final DateTime updatedAt;

  @HiveField(11)
  final List<int> usedPins;

  const Device({
    required this.id,
    required this.name,
    required this.type,
    required this.connectionType,
    this.bluetoothAddress,
    this.wifiIp,
    this.wifiPort,
    this.isEnabled = true,
    this.isConnected = false,
    required this.createdAt,
    required this.updatedAt,
    this.usedPins = const [],
  });

  Device copyWith({
    String? id,
    String? name,
    DeviceType? type,
    ConnectionType? connectionType,
    String? bluetoothAddress,
    String? wifiIp,
    int? wifiPort,
    bool? isEnabled,
    bool? isConnected,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<int>? usedPins,
  }) {
    return Device(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      connectionType: connectionType ?? this.connectionType,
      bluetoothAddress: bluetoothAddress ?? this.bluetoothAddress,
      wifiIp: wifiIp ?? this.wifiIp,
      wifiPort: wifiPort ?? this.wifiPort,
      isEnabled: isEnabled ?? this.isEnabled,
      isConnected: isConnected ?? this.isConnected,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      usedPins: usedPins ?? this.usedPins,
    );
  }

  factory Device.fromJson(Map<String, dynamic> json) => _$DeviceFromJson(json);
  Map<String, dynamic> toJson() => _$DeviceToJson(this);

  bool isPinAvailable(int pin) {
    return !usedPins.contains(pin) && pin >= 0 && pin <= getMaxPins();
  }

  int getMaxPins() {
    switch (type) {
      case DeviceType.arduinoHC05:
        return 13; // Digital pins 0-13
      case DeviceType.esp8266Wifi:
        return 16; // GPIO 0-16
      case DeviceType.esp32Wifi:
      case DeviceType.esp32Bluetooth:
        return 39; // GPIO 0-39
    }
  }

  List<int> getAvailablePins() {
    final maxPins = getMaxPins();
    final availablePins = <int>[];
    for (int i = 0; i <= maxPins; i++) {
      if (isPinAvailable(i)) {
        availablePins.add(i);
      }
    }
    return availablePins;
  }

  String get connectionString {
    switch (connectionType) {
      case ConnectionType.bluetooth:
        return bluetoothAddress ?? 'Unknown';
      case ConnectionType.wifi:
        return '${wifiIp ?? '***********'}:${wifiPort ?? 80}';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Device && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

@HiveType(typeId: 1)
enum DeviceType {
  @HiveField(0)
  arduinoHC05,
  @HiveField(1)
  esp8266Wifi,
  @HiveField(2)
  esp32Wifi,
  @HiveField(3)
  esp32Bluetooth,
}

@HiveType(typeId: 2)
enum ConnectionType {
  @HiveField(0)
  bluetooth,
  @HiveField(1)
  wifi,
}

extension DeviceTypeExtension on DeviceType {
  String get displayName {
    switch (this) {
      case DeviceType.arduinoHC05:
        return 'Arduino HC-05';
      case DeviceType.esp8266Wifi:
        return 'ESP8266 WiFi';
      case DeviceType.esp32Wifi:
        return 'ESP32 WiFi';
      case DeviceType.esp32Bluetooth:
        return 'ESP32 Bluetooth';
    }
  }

  String get description {
    switch (this) {
      case DeviceType.arduinoHC05:
        return 'Arduino with HC-05 Bluetooth module';
      case DeviceType.esp8266Wifi:
        return 'ESP8266 microcontroller with WiFi';
      case DeviceType.esp32Wifi:
        return 'ESP32 microcontroller with WiFi';
      case DeviceType.esp32Bluetooth:
        return 'ESP32 microcontroller with Bluetooth';
    }
  }
}

extension ConnectionTypeExtension on ConnectionType {
  String get displayName {
    switch (this) {
      case ConnectionType.bluetooth:
        return 'Bluetooth';
      case ConnectionType.wifi:
        return 'WiFi';
    }
  }
}
