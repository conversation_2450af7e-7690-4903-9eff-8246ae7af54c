import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/automation_provider.dart';
import '../providers/room_provider.dart';
import '../widgets/automation_card.dart';
import '../widgets/empty_state_widget.dart';
import 'add_automation_screen.dart';

class AutomationsScreen extends ConsumerWidget {
  const AutomationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final automations = ref.watch(automationProvider);
    final hasRooms = ref.watch(hasRoomsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Automations'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.refresh),
            onPressed: () {
              ref.invalidate(automationProvider);
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(automationProvider);
        },
        child: automations.isEmpty
            ? EmptyStateWidget(
                icon: MdiIcons.autorenew,
                title: 'No Automations Yet',
                message: hasRooms
                    ? 'Create smart automations to control your devices automatically'
                    : 'Create rooms with components first, then set up automations',
                actionText: hasRooms ? 'Add Automation' : null,
                onActionPressed: hasRooms ? () => _navigateToAddAutomation(context) : null,
              )
            : Column(
                children: [
                  if (automations.isNotEmpty) ...[
                    Container(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          _buildStatusChip(
                            context,
                            'Total',
                            automations.length.toString(),
                            Colors.blue,
                          ),
                          const SizedBox(width: 12),
                          _buildStatusChip(
                            context,
                            'Active',
                            automations.where((a) => a.isEnabled).length.toString(),
                            Colors.green,
                          ),
                          const SizedBox(width: 12),
                          _buildStatusChip(
                            context,
                            'Disabled',
                            automations.where((a) => !a.isEnabled).length.toString(),
                            Colors.orange,
                          ),
                        ],
                      ),
                    ),
                  ],
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: automations.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: AutomationCard(automation: automations[index]),
                        );
                      },
                    ),
                  ),
                ],
              ),
      ),
      floatingActionButton: hasRooms
          ? FloatingActionButton(
              onPressed: () => _navigateToAddAutomation(context),
              tooltip: 'Add Automation',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildStatusChip(
    BuildContext context,
    String label,
    String value,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToAddAutomation(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddAutomationScreen(),
      ),
    );
  }
}
