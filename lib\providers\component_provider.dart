import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/component.dart';
import '../services/storage_service.dart';

class ComponentNotifier extends StateNotifier<List<Component>> {
  ComponentNotifier(this._storageService) : super([]) {
    _loadComponents();
  }

  final StorageService _storageService;
  static const _uuid = Uuid();

  Future<void> _loadComponents() async {
    try {
      final components = await _storageService.getComponents();
      state = components;
    } catch (e) {
      state = [];
    }
  }

  Future<void> addComponent({
    required String name,
    required ComponentType type,
    required String deviceId,
    required int pin,
    required String iconName,
    String? roomId,
    bool isActiveHigh = true,
    String? unit,
    String? customCommand,
    Map<String, dynamic>? settings,
  }) async {
    if (name.trim().isEmpty) {
      throw ArgumentError('Component name cannot be empty');
    }

    // Check if pin is already used by another component on the same device
    if (state.any((component) => 
        component.deviceId == deviceId && component.pin == pin)) {
      throw ArgumentError('Pin $pin is already in use on this device');
    }

    final now = DateTime.now();
    final component = Component(
      id: _uuid.v4(),
      name: name.trim(),
      type: type,
      deviceId: deviceId,
      roomId: roomId,
      pin: pin,
      iconName: iconName,
      isActiveHigh: isActiveHigh,
      unit: unit,
      customCommand: customCommand,
      settings: settings,
      createdAt: now,
      updatedAt: now,
    );

    state = [...state, component];
    await _storageService.saveComponent(component);
  }

  Future<void> updateComponent(Component updatedComponent) async {
    final index = state.indexWhere((component) => component.id == updatedComponent.id);
    if (index == -1) {
      throw ArgumentError('Component not found');
    }

    final newState = [...state];
    newState[index] = updatedComponent.copyWith(updatedAt: DateTime.now());
    state = newState;
    
    await _storageService.saveComponent(newState[index]);
  }

  Future<void> deleteComponent(String componentId) async {
    final component = state.firstWhere(
      (component) => component.id == componentId,
      orElse: () => throw ArgumentError('Component not found'),
    );

    state = state.where((component) => component.id != componentId).toList();
    await _storageService.deleteComponent(componentId);
  }

  Future<void> toggleComponentEnabled(String componentId) async {
    final component = getComponentById(componentId);
    if (component == null) return;

    await updateComponent(component.copyWith(isEnabled: !component.isEnabled));
  }

  Future<void> updateComponentValue(String componentId, double value) async {
    final component = getComponentById(componentId);
    if (component == null) return;

    await updateComponent(component.copyWith(currentValue: value));
  }

  Component? getComponentById(String componentId) {
    try {
      return state.firstWhere((component) => component.id == componentId);
    } catch (e) {
      return null;
    }
  }

  List<Component> getComponentsByDevice(String deviceId) {
    return state.where((component) => component.deviceId == deviceId).toList();
  }

  List<Component> getComponentsByRoom(String roomId) {
    // This would need to be implemented with room-component relationships
    // For now, return empty list
    return [];
  }

  List<Component> getComponentsByType(ComponentType type) {
    return state.where((component) => component.type == type).toList();
  }

  List<Component> getEnabledComponents() {
    return state.where((component) => component.isEnabled).toList();
  }

  List<Component> getInputComponents() {
    return state.where((component) => component.isInput).toList();
  }

  List<Component> getOutputComponents() {
    return state.where((component) => component.isOutput).toList();
  }

  bool isPinUsed(String deviceId, int pin) {
    return state.any((component) => 
        component.deviceId == deviceId && component.pin == pin);
  }

  List<int> getUsedPins(String deviceId) {
    return state
        .where((component) => component.deviceId == deviceId)
        .map((component) => component.pin)
        .toList();
  }
}

final componentProvider = StateNotifierProvider<ComponentNotifier, List<Component>>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return ComponentNotifier(storageService);
});

// Convenience providers
final enabledComponentsProvider = Provider<List<Component>>((ref) {
  final components = ref.watch(componentProvider);
  return components.where((component) => component.isEnabled).toList();
});

final inputComponentsProvider = Provider<List<Component>>((ref) {
  final components = ref.watch(componentProvider);
  return components.where((component) => component.isInput).toList();
});

final outputComponentsProvider = Provider<List<Component>>((ref) {
  final components = ref.watch(componentProvider);
  return components.where((component) => component.isOutput).toList();
});

// Provider for getting components by device
final componentsByDeviceProvider = Provider.family<List<Component>, String>((ref, deviceId) {
  final components = ref.watch(componentProvider);
  return components.where((component) => component.deviceId == deviceId).toList();
});

// Provider for getting components by room
final componentsByRoomProvider = Provider.family<List<Component>, String>((ref, roomId) {
  // This would need room-component relationship data
  // For now, return empty list
  return [];
});

// Provider for getting a specific component by ID
final componentByIdProvider = Provider.family<Component?, String>((ref, componentId) {
  final components = ref.watch(componentProvider);
  try {
    return components.firstWhere((component) => component.id == componentId);
  } catch (e) {
    return null;
  }
});

// Provider for checking if a pin is used on a device
final isPinUsedProvider = Provider.family<bool, ({String deviceId, int pin})>((ref, params) {
  final components = ref.watch(componentProvider);
  return components.any((component) => 
      component.deviceId == params.deviceId && component.pin == params.pin);
});

// Provider for getting used pins on a device
final usedPinsProvider = Provider.family<List<int>, String>((ref, deviceId) {
  final components = ref.watch(componentProvider);
  return components
      .where((component) => component.deviceId == deviceId)
      .map((component) => component.pin)
      .toList();
});
