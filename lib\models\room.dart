import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'room.g.dart';

@HiveType(typeId: 5)
@JsonSerializable()
class Room {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String iconName;

  @HiveField(3)
  final List<String> componentIds;

  @HiveField(4)
  final bool isEnabled;

  @HiveField(5)
  final DateTime createdAt;

  @HiveField(6)
  final DateTime updatedAt;

  @HiveField(7)
  final String? description;

  @HiveField(8)
  final int? colorValue;

  const Room({
    required this.id,
    required this.name,
    required this.iconName,
    this.componentIds = const [],
    this.isEnabled = true,
    required this.createdAt,
    required this.updatedAt,
    this.description,
    this.colorValue,
  });

  Room copyWith({
    String? id,
    String? name,
    String? iconName,
    List<String>? componentIds,
    bool? isEnabled,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? description,
    int? colorValue,
  }) {
    return Room(
      id: id ?? this.id,
      name: name ?? this.name,
      iconName: iconName ?? this.iconName,
      componentIds: componentIds ?? this.componentIds,
      isEnabled: isEnabled ?? this.isEnabled,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      description: description ?? this.description,
      colorValue: colorValue ?? this.colorValue,
    );
  }

  factory Room.fromJson(Map<String, dynamic> json) => _$RoomFromJson(json);
  Map<String, dynamic> toJson() => _$RoomToJson(this);

  Room addComponent(String componentId) {
    if (componentIds.contains(componentId)) return this;
    
    return copyWith(
      componentIds: [...componentIds, componentId],
      updatedAt: DateTime.now(),
    );
  }

  Room removeComponent(String componentId) {
    if (!componentIds.contains(componentId)) return this;
    
    return copyWith(
      componentIds: componentIds.where((id) => id != componentId).toList(),
      updatedAt: DateTime.now(),
    );
  }

  bool get hasComponents => componentIds.isNotEmpty;

  int get componentCount => componentIds.length;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Room && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

// Predefined room icons
class RoomIcons {
  static const List<String> available = [
    'home',
    'bedroom',
    'living_room',
    'kitchen',
    'bathroom',
    'garage',
    'garden',
    'office',
    'workshop',
    'basement',
    'attic',
    'balcony',
    'dining_room',
    'laundry',
    'storage',
    'gym',
    'library',
    'music_room',
    'game_room',
    'nursery',
  ];

  static String getDisplayName(String iconName) {
    switch (iconName) {
      case 'home':
        return 'Home';
      case 'bedroom':
        return 'Bedroom';
      case 'living_room':
        return 'Living Room';
      case 'kitchen':
        return 'Kitchen';
      case 'bathroom':
        return 'Bathroom';
      case 'garage':
        return 'Garage';
      case 'garden':
        return 'Garden';
      case 'office':
        return 'Office';
      case 'workshop':
        return 'Workshop';
      case 'basement':
        return 'Basement';
      case 'attic':
        return 'Attic';
      case 'balcony':
        return 'Balcony';
      case 'dining_room':
        return 'Dining Room';
      case 'laundry':
        return 'Laundry';
      case 'storage':
        return 'Storage';
      case 'gym':
        return 'Gym';
      case 'library':
        return 'Library';
      case 'music_room':
        return 'Music Room';
      case 'game_room':
        return 'Game Room';
      case 'nursery':
        return 'Nursery';
      default:
        return iconName.replaceAll('_', ' ').split(' ')
            .map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
            .join(' ');
    }
  }
}
