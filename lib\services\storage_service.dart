import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/device.dart';
import '../models/room.dart';
import '../models/component.dart';
import '../models/automation.dart';
import '../models/app_settings.dart';

class StorageService {
  static const String _devicesBoxName = 'devices';
  static const String _roomsBoxName = 'rooms';
  static const String _componentsBoxName = 'components';
  static const String _automationsBoxName = 'automations';
  static const String _settingsBoxName = 'settings';

  late Box<Device> _devicesBox;
  late Box<Room> _roomsBox;
  late Box<Component> _componentsBox;
  late Box<Automation> _automationsBox;
  late Box<AppSettings> _settingsBox;

  Future<void> initialize() async {
    await Hive.initFlutter();
    
    // Register adapters
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(DeviceAdapter());
    }
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(DeviceTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(2)) {
      Hive.registerAdapter(ConnectionTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(ComponentAdapter());
    }
    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(ComponentTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(5)) {
      Hive.registerAdapter(RoomAdapter());
    }
    if (!Hive.isAdapterRegistered(6)) {
      Hive.registerAdapter(AutomationAdapter());
    }
    if (!Hive.isAdapterRegistered(7)) {
      Hive.registerAdapter(AutomationConditionAdapter());
    }
    if (!Hive.isAdapterRegistered(8)) {
      Hive.registerAdapter(AutomationActionAdapter());
    }
    if (!Hive.isAdapterRegistered(9)) {
      Hive.registerAdapter(ComparatorAdapter());
    }
    if (!Hive.isAdapterRegistered(10)) {
      Hive.registerAdapter(AppSettingsAdapter());
    }
    if (!Hive.isAdapterRegistered(11)) {
      Hive.registerAdapter(ThemeModeHiveAdapter());
    }

    // Open boxes
    _devicesBox = await Hive.openBox<Device>(_devicesBoxName);
    _roomsBox = await Hive.openBox<Room>(_roomsBoxName);
    _componentsBox = await Hive.openBox<Component>(_componentsBoxName);
    _automationsBox = await Hive.openBox<Automation>(_automationsBoxName);
    _settingsBox = await Hive.openBox<AppSettings>(_settingsBoxName);
  }

  // Device operations
  Future<List<Device>> getDevices() async {
    return _devicesBox.values.toList();
  }

  Future<void> saveDevice(Device device) async {
    await _devicesBox.put(device.id, device);
  }

  Future<void> deleteDevice(String deviceId) async {
    await _devicesBox.delete(deviceId);
  }

  Device? getDevice(String deviceId) {
    return _devicesBox.get(deviceId);
  }

  // Room operations
  Future<List<Room>> getRooms() async {
    return _roomsBox.values.toList();
  }

  Future<void> saveRoom(Room room) async {
    await _roomsBox.put(room.id, room);
  }

  Future<void> deleteRoom(String roomId) async {
    await _roomsBox.delete(roomId);
  }

  Room? getRoom(String roomId) {
    return _roomsBox.get(roomId);
  }

  // Component operations
  Future<List<Component>> getComponents() async {
    return _componentsBox.values.toList();
  }

  Future<void> saveComponent(Component component) async {
    await _componentsBox.put(component.id, component);
  }

  Future<void> deleteComponent(String componentId) async {
    await _componentsBox.delete(componentId);
  }

  Component? getComponent(String componentId) {
    return _componentsBox.get(componentId);
  }

  Future<List<Component>> getComponentsByRoom(String roomId) async {
    final room = getRoom(roomId);
    if (room == null) return [];
    
    final components = <Component>[];
    for (final componentId in room.componentIds) {
      final component = getComponent(componentId);
      if (component != null) {
        components.add(component);
      }
    }
    return components;
  }

  Future<List<Component>> getComponentsByDevice(String deviceId) async {
    final allComponents = await getComponents();
    return allComponents.where((component) => component.deviceId == deviceId).toList();
  }

  // Automation operations
  Future<List<Automation>> getAutomations() async {
    return _automationsBox.values.toList();
  }

  Future<void> saveAutomation(Automation automation) async {
    await _automationsBox.put(automation.id, automation);
  }

  Future<void> deleteAutomation(String automationId) async {
    await _automationsBox.delete(automationId);
  }

  Automation? getAutomation(String automationId) {
    return _automationsBox.get(automationId);
  }

  Future<List<Automation>> getAutomationsByRoom(String roomId) async {
    final allAutomations = await getAutomations();
    return allAutomations.where((automation) => automation.roomId == roomId).toList();
  }

  // Settings operations
  Future<AppSettings?> getSettings() async {
    final settings = _settingsBox.values.toList();
    return settings.isNotEmpty ? settings.first : null;
  }

  Future<void> saveSettings(AppSettings settings) async {
    await _settingsBox.clear();
    await _settingsBox.put('settings', settings);
  }

  // Utility operations
  Future<void> clearAllData() async {
    await _devicesBox.clear();
    await _roomsBox.clear();
    await _componentsBox.clear();
    await _automationsBox.clear();
    await _settingsBox.clear();
  }

  Future<void> close() async {
    await _devicesBox.close();
    await _roomsBox.close();
    await _componentsBox.close();
    await _automationsBox.close();
    await _settingsBox.close();
  }

  // Data integrity operations
  Future<void> cleanupOrphanedComponents() async {
    final devices = await getDevices();
    final rooms = await getRooms();
    final components = await getComponents();
    
    final deviceIds = devices.map((d) => d.id).toSet();
    final roomComponentIds = rooms.expand((r) => r.componentIds).toSet();
    
    // Remove components that reference non-existent devices
    for (final component in components) {
      if (!deviceIds.contains(component.deviceId)) {
        await deleteComponent(component.id);
      }
    }
    
    // Remove component references from rooms if component doesn't exist
    for (final room in rooms) {
      final validComponentIds = room.componentIds
          .where((id) => components.any((c) => c.id == id))
          .toList();
      
      if (validComponentIds.length != room.componentIds.length) {
        await saveRoom(room.copyWith(componentIds: validComponentIds));
      }
    }
  }

  Future<void> cleanupOrphanedAutomations() async {
    final rooms = await getRooms();
    final components = await getComponents();
    final automations = await getAutomations();
    
    final roomIds = rooms.map((r) => r.id).toSet();
    final componentIds = components.map((c) => c.id).toSet();
    
    // Remove automations that reference non-existent rooms or components
    for (final automation in automations) {
      if (!roomIds.contains(automation.roomId) ||
          !componentIds.contains(automation.condition.componentId) ||
          !componentIds.contains(automation.action.componentId)) {
        await deleteAutomation(automation.id);
      }
    }
  }
}

final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});
