import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:file_picker/file_picker.dart';
import '../models/device.dart';
import '../models/room.dart';
import '../models/component.dart';
import '../models/automation.dart';
import '../models/app_settings.dart';

class BackupService {
  static const String _backupFileName = 'arduverse_backup.json';
  static const int _currentBackupVersion = 1;

  // Export all app data to a JSON file
  static Future<String?> exportData({
    required List<Device> devices,
    required List<Room> rooms,
    required List<Component> components,
    required List<Automation> automations,
    required AppSettings settings,
  }) async {
    try {
      final backupData = {
        'version': _currentBackupVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'app_info': {
          'name': 'Arduverse',
          'version': '1.0.0',
        },
        'data': {
          'devices': devices.map((d) => d.toJson()).toList(),
          'rooms': rooms.map((r) => r.toJson()).toList(),
          'components': components.map((c) => c.toJson()).toList(),
          'automations': automations.map((a) => a.toJson()).toList(),
          'settings': settings.toJson(),
        },
      };

      final jsonString = const JsonEncoder.withIndent('  ').convert(backupData);
      
      // Get the downloads directory
      final directory = await getExternalStorageDirectory();
      if (directory == null) {
        throw Exception('Could not access storage directory');
      }

      // Create the backup file
      final file = File('${directory.path}/$_backupFileName');
      await file.writeAsString(jsonString);

      // Share the file
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Arduverse backup file - ${DateTime.now().toString().split('.')[0]}',
        subject: 'Arduverse Backup',
      );

      return file.path;
    } catch (e) {
      debugPrint('Export error: $e');
      return null;
    }
  }

  // Import data from a JSON file
  static Future<BackupData?> importData() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        return null;
      }

      final file = File(result.files.first.path!);
      final jsonString = await file.readAsString();
      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;

      // Validate backup format
      if (!_isValidBackup(jsonData)) {
        throw Exception('Invalid backup file format');
      }

      final data = jsonData['data'] as Map<String, dynamic>;
      
      return BackupData(
        devices: (data['devices'] as List)
            .map((json) => Device.fromJson(json))
            .toList(),
        rooms: (data['rooms'] as List)
            .map((json) => Room.fromJson(json))
            .toList(),
        components: (data['components'] as List)
            .map((json) => Component.fromJson(json))
            .toList(),
        automations: (data['automations'] as List)
            .map((json) => Automation.fromJson(json))
            .toList(),
        settings: AppSettings.fromJson(data['settings']),
        backupVersion: jsonData['version'] as int,
        timestamp: DateTime.parse(jsonData['timestamp'] as String),
      );
    } catch (e) {
      debugPrint('Import error: $e');
      return null;
    }
  }

  // Validate backup file structure
  static bool _isValidBackup(Map<String, dynamic> jsonData) {
    try {
      // Check required fields
      if (!jsonData.containsKey('version') ||
          !jsonData.containsKey('timestamp') ||
          !jsonData.containsKey('data')) {
        return false;
      }

      final data = jsonData['data'] as Map<String, dynamic>;
      
      // Check required data sections
      final requiredSections = ['devices', 'rooms', 'components', 'automations', 'settings'];
      for (final section in requiredSections) {
        if (!data.containsKey(section)) {
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  // Create a quick backup for safety before major operations
  static Future<String?> createQuickBackup({
    required List<Device> devices,
    required List<Room> rooms,
    required List<Component> components,
    required List<Automation> automations,
    required AppSettings settings,
  }) async {
    try {
      final backupData = {
        'version': _currentBackupVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'type': 'quick_backup',
        'data': {
          'devices': devices.map((d) => d.toJson()).toList(),
          'rooms': rooms.map((r) => r.toJson()).toList(),
          'components': components.map((c) => c.toJson()).toList(),
          'automations': automations.map((a) => a.toJson()).toList(),
          'settings': settings.toJson(),
        },
      };

      final jsonString = jsonEncode(backupData);
      
      // Get the app documents directory
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');
      
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      // Create timestamped backup file
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final file = File('${backupDir.path}/quick_backup_$timestamp.json');
      await file.writeAsString(jsonString);

      // Keep only the last 5 quick backups
      await _cleanupOldBackups(backupDir);

      return file.path;
    } catch (e) {
      debugPrint('Quick backup error: $e');
      return null;
    }
  }

  // Clean up old backup files
  static Future<void> _cleanupOldBackups(Directory backupDir) async {
    try {
      final files = await backupDir
          .list()
          .where((entity) => entity is File && entity.path.contains('quick_backup_'))
          .cast<File>()
          .toList();

      if (files.length > 5) {
        // Sort by modification time (oldest first)
        files.sort((a, b) => a.lastModifiedSync().compareTo(b.lastModifiedSync()));
        
        // Delete oldest files, keeping only the last 5
        for (int i = 0; i < files.length - 5; i++) {
          await files[i].delete();
        }
      }
    } catch (e) {
      debugPrint('Cleanup error: $e');
    }
  }

  // Get backup file info
  static Future<List<BackupInfo>> getAvailableBackups() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');
      
      if (!await backupDir.exists()) {
        return [];
      }

      final files = await backupDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.json'))
          .cast<File>()
          .toList();

      final backupInfos = <BackupInfo>[];
      
      for (final file in files) {
        try {
          final content = await file.readAsString();
          final jsonData = jsonDecode(content) as Map<String, dynamic>;
          
          backupInfos.add(BackupInfo(
            filePath: file.path,
            fileName: file.path.split('/').last,
            timestamp: DateTime.parse(jsonData['timestamp'] as String),
            version: jsonData['version'] as int,
            type: jsonData['type'] as String? ?? 'manual',
            size: await file.length(),
          ));
        } catch (e) {
          debugPrint('Error reading backup file ${file.path}: $e');
        }
      }

      // Sort by timestamp (newest first)
      backupInfos.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
      return backupInfos;
    } catch (e) {
      debugPrint('Error getting backup list: $e');
      return [];
    }
  }
}

// Data classes for backup operations
class BackupData {
  final List<Device> devices;
  final List<Room> rooms;
  final List<Component> components;
  final List<Automation> automations;
  final AppSettings settings;
  final int backupVersion;
  final DateTime timestamp;

  BackupData({
    required this.devices,
    required this.rooms,
    required this.components,
    required this.automations,
    required this.settings,
    required this.backupVersion,
    required this.timestamp,
  });
}

class BackupInfo {
  final String filePath;
  final String fileName;
  final DateTime timestamp;
  final int version;
  final String type;
  final int size;

  BackupInfo({
    required this.filePath,
    required this.fileName,
    required this.timestamp,
    required this.version,
    required this.type,
    required this.size,
  });

  String get formattedSize {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  String get formattedDate {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
  }
}
