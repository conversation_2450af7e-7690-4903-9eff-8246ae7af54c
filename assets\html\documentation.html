<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arduverse - Complete Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #e67e22, #d35400);
            color: white;
            padding: 40px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .nav {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav h3 {
            margin-bottom: 15px;
            color: #e67e22;
        }
        
        .nav ul {
            list-style: none;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }
        
        .nav a {
            color: #333;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav a:hover {
            background: #f0f0f0;
        }
        
        .section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #e67e22;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #d35400;
            margin: 25px 0 15px 0;
            font-size: 1.3em;
        }
        
        .section h4 {
            color: #555;
            margin: 20px 0 10px 0;
            font-size: 1.1em;
        }
        
        .code {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .warning::before {
            content: "⚠️ ";
            font-weight: bold;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .info::before {
            content: "ℹ️ ";
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .success::before {
            content: "✅ ";
            font-weight: bold;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #e67e22;
        }
        
        .feature-card h4 {
            color: #e67e22;
            margin-bottom: 10px;
        }
        
        .step {
            background: #f8f9fa;
            border-left: 4px solid #e67e22;
            padding: 15px;
            margin: 15px 0;
        }
        
        .step-number {
            background: #e67e22;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .footer {
            text-align: center;
            padding: 40px 20px;
            color: #666;
            border-top: 1px solid #ddd;
            margin-top: 50px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .nav ul {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Arduverse</h1>
            <p>Professional IoT Controller - Complete Documentation</p>
            <p>Version 1.0.0 | Developed by SK Raihan - SKR Electronics Lab</p>
        </div>

        <div class="nav">
            <h3>📋 Quick Navigation</h3>
            <ul>
                <li><a href="#overview">📖 Overview</a></li>
                <li><a href="#features">✨ Features</a></li>
                <li><a href="#installation">📱 Installation</a></li>
                <li><a href="#setup">⚙️ Setup Guide</a></li>
                <li><a href="#devices">🔌 Device Management</a></li>
                <li><a href="#components">🧩 Components</a></li>
                <li><a href="#rooms">🏠 Room Organization</a></li>
                <li><a href="#automations">🤖 Automations</a></li>
                <li><a href="#troubleshooting">🔧 Troubleshooting</a></li>
                <li><a href="#arduino-code">💻 Arduino Code</a></li>
                <li><a href="#esp32-code">📡 ESP32 Code</a></li>
                <li><a href="#support">📞 Support</a></li>
            </ul>
        </div>

        <div class="section" id="overview">
            <h2>📖 Overview</h2>
            <p>Arduverse is a professional Flutter-based IoT controller application designed to manage and control Arduino and ESP32 devices. It provides a modern, intuitive interface for controlling various electronic components through WiFi and Bluetooth connections.</p>
            
            <div class="info">
                <strong>Key Benefits:</strong>
                <ul>
                    <li>Real-time device monitoring and control</li>
                    <li>Professional UI with dark/light theme support</li>
                    <li>Pin conflict prevention system</li>
                    <li>Room-based organization</li>
                    <li>Automation capabilities</li>
                    <li>Data backup and restore</li>
                </ul>
            </div>
        </div>

        <div class="section" id="features">
            <h2>✨ Features</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔌 Multi-Device Support</h4>
                    <p>Control ESP32, Arduino Uno, Nano, and Mega boards via WiFi and Bluetooth connections.</p>
                </div>
                
                <div class="feature-card">
                    <h4>🧩 Component Management</h4>
                    <p>Support for lights, fans, servos, motors, relays, sensors, buzzers, LEDs, and custom components.</p>
                </div>
                
                <div class="feature-card">
                    <h4>🏠 Room Organization</h4>
                    <p>Organize components into logical rooms for better management and control.</p>
                </div>
                
                <div class="feature-card">
                    <h4>🤖 Smart Automations</h4>
                    <p>Create IF-THEN automations based on sensor readings, time, or manual triggers.</p>
                </div>
                
                <div class="feature-card">
                    <h4>📱 Modern UI</h4>
                    <p>Beautiful, responsive interface with dark/light theme support and haptic feedback.</p>
                </div>
                
                <div class="feature-card">
                    <h4>🔒 Data Security</h4>
                    <p>Local data storage with backup/restore capabilities and privacy protection.</p>
                </div>
            </div>
        </div>

        <div class="section" id="installation">
            <h2>📱 Installation</h2>
            
            <h3>System Requirements</h3>
            <ul>
                <li><strong>Android:</strong> 6.0 (API level 23) or higher</li>
                <li><strong>iOS:</strong> 12.0 or higher</li>
                <li><strong>Storage:</strong> 50MB free space</li>
                <li><strong>RAM:</strong> 2GB minimum, 4GB recommended</li>
                <li><strong>Network:</strong> WiFi or mobile data for device communication</li>
            </ul>
            
            <h3>Installation Steps</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Download the APK</strong><br>
                Download the latest Arduverse APK from the official source or build from source code.
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>Enable Unknown Sources</strong><br>
                Go to Settings > Security > Unknown Sources and enable installation from unknown sources.
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>Install the App</strong><br>
                Tap the downloaded APK file and follow the installation prompts.
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>Grant Permissions</strong><br>
                Allow the app to access Bluetooth, location, and storage when prompted.
            </div>
            
            <div class="warning">
                <strong>Important:</strong> Make sure to download the app from trusted sources only. The official version is signed with our certificate.
            </div>
        </div>

        <div class="section" id="setup">
            <h2>⚙️ Setup Guide</h2>
            
            <h3>First Launch Setup</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Welcome Screen</strong><br>
                Launch the app and complete the initial setup wizard.
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>Permissions</strong><br>
                Grant all required permissions for optimal functionality:
                <ul>
                    <li>Bluetooth - For Bluetooth device communication</li>
                    <li>Location - Required for Bluetooth scanning on Android</li>
                    <li>Storage - For backup and restore functionality</li>
                </ul>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>Network Configuration</strong><br>
                Configure default WiFi settings in Settings > Connection:
                <ul>
                    <li>Default IP: Your ESP32/Arduino IP address</li>
                    <li>Default Port: Usually 80 for HTTP communication</li>
                    <li>Connection Timeout: 10-30 seconds recommended</li>
                </ul>
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>Add Your First Device</strong><br>
                Navigate to Devices tab and tap "Add Device" to connect your first Arduino or ESP32.
            </div>
        </div>

        <div class="section" id="devices">
            <h2>🔌 Device Management</h2>

            <h3>Supported Devices</h3>
            <table>
                <tr>
                    <th>Device Type</th>
                    <th>Connection</th>
                    <th>Max Pins</th>
                    <th>Notes</th>
                </tr>
                <tr>
                    <td>ESP32 WiFi</td>
                    <td>WiFi</td>
                    <td>30</td>
                    <td>Recommended for WiFi projects</td>
                </tr>
                <tr>
                    <td>ESP32 Bluetooth</td>
                    <td>Bluetooth</td>
                    <td>30</td>
                    <td>Good for portable projects</td>
                </tr>
                <tr>
                    <td>Arduino Uno</td>
                    <td>Bluetooth</td>
                    <td>13</td>
                    <td>Classic Arduino board</td>
                </tr>
                <tr>
                    <td>Arduino Nano</td>
                    <td>Bluetooth</td>
                    <td>13</td>
                    <td>Compact size</td>
                </tr>
                <tr>
                    <td>Arduino Mega</td>
                    <td>Bluetooth</td>
                    <td>53</td>
                    <td>More pins available</td>
                </tr>
            </table>

            <h3>Adding a Device</h3>

            <div class="step">
                <span class="step-number">1</span>
                <strong>Navigate to Devices</strong><br>
                Tap the "Devices" tab in the bottom navigation.
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>Add Device</strong><br>
                Tap the "+" button or "Add Device" button.
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>Configure Device</strong><br>
                <ul>
                    <li>Enter a descriptive name</li>
                    <li>Select device type (ESP32, Arduino, etc.)</li>
                    <li>Choose connection type (WiFi or Bluetooth)</li>
                </ul>
            </div>

            <div class="step">
                <span class="step-number">4</span>
                <strong>Connection Setup</strong><br>
                <strong>For WiFi:</strong> Enter IP address and port<br>
                <strong>For Bluetooth:</strong> Scan and select your device
            </div>

            <div class="warning">
                Make sure your device is powered on and running the appropriate code before attempting to connect.
            </div>
        </div>

        <div class="section" id="components">
            <h2>🧩 Components</h2>

            <h3>Supported Component Types</h3>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>💡 Light</h4>
                    <p>Basic on/off lighting control. Perfect for LEDs, bulbs, and lighting systems.</p>
                </div>

                <div class="feature-card">
                    <h4>🌀 Fan</h4>
                    <p>Fan speed control with variable speed settings and direction control.</p>
                </div>

                <div class="feature-card">
                    <h4>⚙️ Servo</h4>
                    <p>Precise angle control for servo motors. Set specific angles from 0-180 degrees.</p>
                </div>

                <div class="feature-card">
                    <h4>🔧 Motor</h4>
                    <p>DC motor control with speed and direction. Supports PWM speed control.</p>
                </div>

                <div class="feature-card">
                    <h4>🔌 Relay</h4>
                    <p>High-power switching for appliances and heavy loads. Safety-focused design.</p>
                </div>

                <div class="feature-card">
                    <h4>📊 Sensor</h4>
                    <p>Read analog and digital sensor values. Supports temperature, humidity, light sensors.</p>
                </div>

                <div class="feature-card">
                    <h4>🔊 Buzzer</h4>
                    <p>Sound alerts and notifications. Supports tone generation and patterns.</p>
                </div>

                <div class="feature-card">
                    <h4>💡 LED</h4>
                    <p>Individual LED control with brightness and color options (RGB support).</p>
                </div>

                <div class="feature-card">
                    <h4>🛠️ Custom</h4>
                    <p>Define your own component with custom commands and behaviors.</p>
                </div>
            </div>

            <h3>Adding Components</h3>

            <div class="step">
                <span class="step-number">1</span>
                <strong>Select Room</strong><br>
                Navigate to a room or create a new one first.
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>Add Component</strong><br>
                Tap the "+" button in the room view.
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>Configure Component</strong><br>
                <ul>
                    <li>Enter component name</li>
                    <li>Select connected device</li>
                    <li>Choose component type</li>
                    <li>Select available pin</li>
                    <li>Configure settings (active high/low, units, etc.)</li>
                </ul>
            </div>

            <div class="info">
                The app automatically prevents pin conflicts by showing only available pins for each device.
            </div>
        </div>

        <div class="section" id="troubleshooting">
            <h2>🔧 Troubleshooting</h2>

            <h3>Common Issues</h3>

            <h4>Device Connection Problems</h4>
            <div class="warning">
                <strong>Problem:</strong> Cannot connect to device<br>
                <strong>Solutions:</strong>
                <ul>
                    <li>Check device power and network connection</li>
                    <li>Verify IP address and port settings</li>
                    <li>Ensure device code is running correctly</li>
                    <li>Check firewall settings</li>
                    <li>Try increasing connection timeout</li>
                </ul>
            </div>

            <h4>Bluetooth Issues</h4>
            <div class="warning">
                <strong>Problem:</strong> Bluetooth device not found<br>
                <strong>Solutions:</strong>
                <ul>
                    <li>Enable Bluetooth on your phone</li>
                    <li>Grant location permissions</li>
                    <li>Make sure device is in pairing mode</li>
                    <li>Clear Bluetooth cache in Android settings</li>
                    <li>Restart both devices</li>
                </ul>
            </div>

            <h4>Component Control Issues</h4>
            <div class="warning">
                <strong>Problem:</strong> Component not responding<br>
                <strong>Solutions:</strong>
                <ul>
                    <li>Check pin connections</li>
                    <li>Verify active high/low settings</li>
                    <li>Test with simple LED first</li>
                    <li>Check device code for pin configuration</li>
                    <li>Monitor serial output for errors</li>
                </ul>
            </div>

            <h3>Debug Features</h3>
            <p>Enable debug mode in Settings > Debug to access:</p>
            <ul>
                <li>System information display</li>
                <li>Storage usage details</li>
                <li>Network diagnostics</li>
                <li>Connection logs</li>
            </ul>
        </div>

        <div class="footer">
            <p>© 2024 SK Raihan - SKR Electronics Lab</p>
            <p>Arduverse v1.0.0 - Professional IoT Controller</p>
            <p>For support: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>YouTube: <a href="https://www.youtube.com/@skr_electronics_lab">SKR Electronics Lab</a></p>
        </div>
    </div>
</body>
</html>
