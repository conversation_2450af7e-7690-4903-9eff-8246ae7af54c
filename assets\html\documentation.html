<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arduverse - Complete Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #e67e22, #d35400);
            color: white;
            padding: 40px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .nav {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav h3 {
            margin-bottom: 15px;
            color: #e67e22;
        }
        
        .nav ul {
            list-style: none;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }
        
        .nav a {
            color: #333;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav a:hover {
            background: #f0f0f0;
        }
        
        .section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #e67e22;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #d35400;
            margin: 25px 0 15px 0;
            font-size: 1.3em;
        }
        
        .section h4 {
            color: #555;
            margin: 20px 0 10px 0;
            font-size: 1.1em;
        }
        
        .code {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .warning::before {
            content: "⚠️ ";
            font-weight: bold;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .info::before {
            content: "ℹ️ ";
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .success::before {
            content: "✅ ";
            font-weight: bold;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #e67e22;
        }
        
        .feature-card h4 {
            color: #e67e22;
            margin-bottom: 10px;
        }
        
        .step {
            background: #f8f9fa;
            border-left: 4px solid #e67e22;
            padding: 15px;
            margin: 15px 0;
        }
        
        .step-number {
            background: #e67e22;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .footer {
            text-align: center;
            padding: 40px 20px;
            color: #666;
            border-top: 1px solid #ddd;
            margin-top: 50px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .nav ul {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Arduverse</h1>
            <p>Professional IoT Controller - Complete Documentation</p>
            <p>Version 1.0.0 | Developed by SK Raihan - SKR Electronics Lab</p>
        </div>

        <div class="nav">
            <h3>📋 Quick Navigation</h3>
            <ul>
                <li><a href="#overview">📖 Overview</a></li>
                <li><a href="#features">✨ Features</a></li>
                <li><a href="#installation">📱 Installation</a></li>
                <li><a href="#setup">⚙️ Setup Guide</a></li>
                <li><a href="#devices">🔌 Device Management</a></li>
                <li><a href="#components">🧩 Components</a></li>
                <li><a href="#rooms">🏠 Room Organization</a></li>
                <li><a href="#automations">🤖 Automations</a></li>
                <li><a href="#troubleshooting">🔧 Troubleshooting</a></li>
                <li><a href="#programming-guide">📚 Programming Guide</a></li>
                <li><a href="#arduino-code">💻 Arduino Code</a></li>
                <li><a href="#esp32-code">📡 ESP32 Code</a></li>
                <li><a href="#support">📞 Support</a></li>
            </ul>
        </div>

        <div class="section" id="overview">
            <h2>📖 Overview</h2>
            <p>Arduverse is a professional Flutter-based IoT controller application designed to manage and control Arduino and ESP32 devices. It provides a modern, intuitive interface for controlling various electronic components through WiFi and Bluetooth connections.</p>
            
            <div class="info">
                <strong>Key Benefits:</strong>
                <ul>
                    <li>Real-time device monitoring and control</li>
                    <li>Professional UI with dark/light theme support</li>
                    <li>Pin conflict prevention system</li>
                    <li>Room-based organization</li>
                    <li>Automation capabilities</li>
                    <li>Data backup and restore</li>
                </ul>
            </div>
        </div>

        <div class="section" id="features">
            <h2>✨ Features</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔌 Multi-Device Support</h4>
                    <p>Control ESP32, Arduino Uno, Nano, and Mega boards via WiFi and Bluetooth connections.</p>
                </div>
                
                <div class="feature-card">
                    <h4>🧩 Component Management</h4>
                    <p>Support for lights, fans, servos, motors, relays, sensors, buzzers, LEDs, and custom components.</p>
                </div>
                
                <div class="feature-card">
                    <h4>🏠 Room Organization</h4>
                    <p>Organize components into logical rooms for better management and control.</p>
                </div>
                
                <div class="feature-card">
                    <h4>🤖 Smart Automations</h4>
                    <p>Create IF-THEN automations based on sensor readings, time, or manual triggers.</p>
                </div>
                
                <div class="feature-card">
                    <h4>📱 Modern UI</h4>
                    <p>Beautiful, responsive interface with dark/light theme support and haptic feedback.</p>
                </div>
                
                <div class="feature-card">
                    <h4>🔒 Data Security</h4>
                    <p>Local data storage with backup/restore capabilities and privacy protection.</p>
                </div>
            </div>
        </div>

        <div class="section" id="installation">
            <h2>📱 Installation</h2>
            
            <h3>System Requirements</h3>
            <ul>
                <li><strong>Android:</strong> 6.0 (API level 23) or higher</li>
                <li><strong>iOS:</strong> 12.0 or higher</li>
                <li><strong>Storage:</strong> 50MB free space</li>
                <li><strong>RAM:</strong> 2GB minimum, 4GB recommended</li>
                <li><strong>Network:</strong> WiFi or mobile data for device communication</li>
            </ul>
            
            <h3>Installation Steps</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Download the APK</strong><br>
                Download the latest Arduverse APK from the official source or build from source code.
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>Enable Unknown Sources</strong><br>
                Go to Settings > Security > Unknown Sources and enable installation from unknown sources.
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>Install the App</strong><br>
                Tap the downloaded APK file and follow the installation prompts.
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>Grant Permissions</strong><br>
                Allow the app to access Bluetooth, location, and storage when prompted.
            </div>
            
            <div class="warning">
                <strong>Important:</strong> Make sure to download the app from trusted sources only. The official version is signed with our certificate.
            </div>
        </div>

        <div class="section" id="setup">
            <h2>⚙️ Setup Guide</h2>
            
            <h3>First Launch Setup</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Welcome Screen</strong><br>
                Launch the app and complete the initial setup wizard.
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>Permissions</strong><br>
                Grant all required permissions for optimal functionality:
                <ul>
                    <li>Bluetooth - For Bluetooth device communication</li>
                    <li>Location - Required for Bluetooth scanning on Android</li>
                    <li>Storage - For backup and restore functionality</li>
                </ul>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>Network Configuration</strong><br>
                Configure default WiFi settings in Settings > Connection:
                <ul>
                    <li>Default IP: Your ESP32/Arduino IP address</li>
                    <li>Default Port: Usually 80 for HTTP communication</li>
                    <li>Connection Timeout: 10-30 seconds recommended</li>
                </ul>
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>Add Your First Device</strong><br>
                Navigate to Devices tab and tap "Add Device" to connect your first Arduino or ESP32.
            </div>
        </div>

        <div class="section" id="devices">
            <h2>🔌 Device Management</h2>

            <h3>Supported Devices</h3>
            <table>
                <tr>
                    <th>Device Type</th>
                    <th>Connection</th>
                    <th>Max Pins</th>
                    <th>Notes</th>
                </tr>
                <tr>
                    <td>ESP32 WiFi</td>
                    <td>WiFi</td>
                    <td>30</td>
                    <td>Recommended for WiFi projects</td>
                </tr>
                <tr>
                    <td>ESP32 Bluetooth</td>
                    <td>Bluetooth</td>
                    <td>30</td>
                    <td>Good for portable projects</td>
                </tr>
                <tr>
                    <td>Arduino Uno</td>
                    <td>Bluetooth</td>
                    <td>13</td>
                    <td>Classic Arduino board</td>
                </tr>
                <tr>
                    <td>Arduino Nano</td>
                    <td>Bluetooth</td>
                    <td>13</td>
                    <td>Compact size</td>
                </tr>
                <tr>
                    <td>Arduino Mega</td>
                    <td>Bluetooth</td>
                    <td>53</td>
                    <td>More pins available</td>
                </tr>
            </table>

            <h3>Adding a Device</h3>

            <div class="step">
                <span class="step-number">1</span>
                <strong>Navigate to Devices</strong><br>
                Tap the "Devices" tab in the bottom navigation.
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>Add Device</strong><br>
                Tap the "+" button or "Add Device" button.
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>Configure Device</strong><br>
                <ul>
                    <li>Enter a descriptive name</li>
                    <li>Select device type (ESP32, Arduino, etc.)</li>
                    <li>Choose connection type (WiFi or Bluetooth)</li>
                </ul>
            </div>

            <div class="step">
                <span class="step-number">4</span>
                <strong>Connection Setup</strong><br>
                <strong>For WiFi:</strong> Enter IP address and port<br>
                <strong>For Bluetooth:</strong> Scan and select your device
            </div>

            <div class="warning">
                Make sure your device is powered on and running the appropriate code before attempting to connect.
            </div>
        </div>

        <div class="section" id="components">
            <h2>🧩 Components</h2>

            <h3>Supported Component Types</h3>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>💡 Light</h4>
                    <p>Basic on/off lighting control. Perfect for LEDs, bulbs, and lighting systems.</p>
                </div>

                <div class="feature-card">
                    <h4>🌀 Fan</h4>
                    <p>Fan speed control with variable speed settings and direction control.</p>
                </div>

                <div class="feature-card">
                    <h4>⚙️ Servo</h4>
                    <p>Precise angle control for servo motors. Set specific angles from 0-180 degrees.</p>
                </div>

                <div class="feature-card">
                    <h4>🔧 Motor</h4>
                    <p>DC motor control with speed and direction. Supports PWM speed control.</p>
                </div>

                <div class="feature-card">
                    <h4>🔌 Relay</h4>
                    <p>High-power switching for appliances and heavy loads. Safety-focused design.</p>
                </div>

                <div class="feature-card">
                    <h4>📊 Sensor</h4>
                    <p>Read analog and digital sensor values. Supports temperature, humidity, light sensors.</p>
                </div>

                <div class="feature-card">
                    <h4>🔊 Buzzer</h4>
                    <p>Sound alerts and notifications. Supports tone generation and patterns.</p>
                </div>

                <div class="feature-card">
                    <h4>💡 LED</h4>
                    <p>Individual LED control with brightness and color options (RGB support).</p>
                </div>

                <div class="feature-card">
                    <h4>🛠️ Custom</h4>
                    <p>Define your own component with custom commands and behaviors.</p>
                </div>
            </div>

            <h3>Adding Components</h3>

            <div class="step">
                <span class="step-number">1</span>
                <strong>Select Room</strong><br>
                Navigate to a room or create a new one first.
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>Add Component</strong><br>
                Tap the "+" button in the room view.
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>Configure Component</strong><br>
                <ul>
                    <li>Enter component name</li>
                    <li>Select connected device</li>
                    <li>Choose component type</li>
                    <li>Select available pin</li>
                    <li>Configure settings (active high/low, units, etc.)</li>
                </ul>
            </div>

            <div class="info">
                The app automatically prevents pin conflicts by showing only available pins for each device.
            </div>
        </div>

        <div class="section" id="troubleshooting">
            <h2>🔧 Troubleshooting</h2>

            <h3>Common Issues</h3>

            <h4>Device Connection Problems</h4>
            <div class="warning">
                <strong>Problem:</strong> Cannot connect to device<br>
                <strong>Solutions:</strong>
                <ul>
                    <li>Check device power and network connection</li>
                    <li>Verify IP address and port settings</li>
                    <li>Ensure device code is running correctly</li>
                    <li>Check firewall settings</li>
                    <li>Try increasing connection timeout</li>
                </ul>
            </div>

            <h4>Bluetooth Issues</h4>
            <div class="warning">
                <strong>Problem:</strong> Bluetooth device not found<br>
                <strong>Solutions:</strong>
                <ul>
                    <li>Enable Bluetooth on your phone</li>
                    <li>Grant location permissions</li>
                    <li>Make sure device is in pairing mode</li>
                    <li>Clear Bluetooth cache in Android settings</li>
                    <li>Restart both devices</li>
                </ul>
            </div>

            <h4>Component Control Issues</h4>
            <div class="warning">
                <strong>Problem:</strong> Component not responding<br>
                <strong>Solutions:</strong>
                <ul>
                    <li>Check pin connections</li>
                    <li>Verify active high/low settings</li>
                    <li>Test with simple LED first</li>
                    <li>Check device code for pin configuration</li>
                    <li>Monitor serial output for errors</li>
                </ul>
            </div>

            <h3>Debug Features</h3>
            <p>Enable debug mode in Settings > Debug to access:</p>
            <ul>
                <li>System information display</li>
                <li>Storage usage details</li>
                <li>Network diagnostics</li>
                <li>Connection logs</li>
            </ul>
        </div>

        <!-- Programming Guide Section -->
        <div class="section" id="programming-guide">
            <h2>📚 Complete Programming Guide</h2>
            <p>Learn to program Arduino and ESP32 boards for IoT projects with this comprehensive tutorial.</p>

            <div class="subsection">
                <h3>🚀 Getting Started</h3>
                <h4>Prerequisites</h4>
                <ul>
                    <li>A computer (Windows, macOS, or Linux)</li>
                    <li>An Arduino or ESP32 board</li>
                    <li>USB cable for programming</li>
                    <li>Basic understanding of electronics (helpful but not required)</li>
                </ul>

                <h4>Required Software</h4>
                <ol>
                    <li><strong>Arduino IDE</strong> (recommended) or <strong>PlatformIO</strong></li>
                    <li><strong>Board drivers</strong> (usually auto-installed)</li>
                    <li><strong>Arduverse mobile app</strong> for testing</li>
                </ol>
            </div>

            <div class="subsection">
                <h3>🔧 Supported Hardware</h3>

                <h4>Arduino Uno R3</h4>
                <ul>
                    <li><strong>Microcontroller:</strong> ATmega328P</li>
                    <li><strong>Operating Voltage:</strong> 5V</li>
                    <li><strong>Digital I/O Pins:</strong> 14 (6 PWM)</li>
                    <li><strong>Analog Input Pins:</strong> 6</li>
                    <li><strong>Flash Memory:</strong> 32KB</li>
                    <li><strong>Clock Speed:</strong> 16MHz</li>
                </ul>

                <h4>ESP32 Development Board</h4>
                <ul>
                    <li><strong>Microcontroller:</strong> ESP32</li>
                    <li><strong>Operating Voltage:</strong> 3.3V</li>
                    <li><strong>Digital I/O Pins:</strong> 34</li>
                    <li><strong>Analog Input Pins:</strong> 18</li>
                    <li><strong>Flash Memory:</strong> 4MB</li>
                    <li><strong>WiFi:</strong> 802.11 b/g/n</li>
                    <li><strong>Bluetooth:</strong> v4.2 BR/EDR and BLE</li>
                    <li><strong>Clock Speed:</strong> 240MHz (dual-core)</li>
                </ul>
            </div>

            <div class="subsection">
                <h3>⚙️ Arduino IDE Setup</h3>

                <h4>Step 1: Download and Install</h4>
                <ol>
                    <li>Visit <a href="https://arduino.cc/downloads" target="_blank">arduino.cc/downloads</a></li>
                    <li>Download the latest version for your operating system</li>
                    <li>Install following the setup wizard</li>
                </ol>

                <h4>Step 2: Install Board Packages</h4>
                <p><strong>For ESP32:</strong></p>
                <ol>
                    <li>Open Arduino IDE</li>
                    <li>Go to <strong>File > Preferences</strong></li>
                    <li>Add this URL to "Additional Board Manager URLs":</li>
                    <li><code>https://dl.espressif.com/dl/package_esp32_index.json</code></li>
                    <li>Go to <strong>Tools > Board > Boards Manager</strong></li>
                    <li>Search for "ESP32" and install "ESP32 by Espressif Systems"</li>
                </ol>

                <h4>Step 3: Install Required Libraries</h4>
                <p>Go to <strong>Tools > Manage Libraries</strong> and install:</p>
                <ul>
                    <li><strong>WiFi</strong> (for ESP32/ESP8266)</li>
                    <li><strong>BluetoothSerial</strong> (for ESP32)</li>
                    <li><strong>ArduinoJson</strong> (for data handling)</li>
                    <li><strong>DHT sensor library</strong> (for temperature sensors)</li>
                    <li><strong>Servo</strong> (for servo motors)</li>
                </ul>
            </div>

            <div class="subsection">
                <h3>💻 Basic Programming Concepts</h3>

                <h4>Arduino Program Structure</h4>
                <p>Every Arduino program (sketch) has two main functions:</p>
                <pre><code>void setup() {
  // This function runs once when the board starts
  // Initialize pins, serial communication, etc.
}

void loop() {
  // This function runs repeatedly
  // Main program logic goes here
}</code></pre>

                <h4>Data Types</h4>
                <pre><code>int number = 42;           // Integer (-32,768 to 32,767)
float temperature = 25.5;  // Floating point number
bool isOn = true;          // Boolean (true/false)
char letter = 'A';         // Single character
String text = "Hello";     // Text string</code></pre>

                <h4>Pin Operations</h4>
                <pre><code>// Digital pins
pinMode(13, OUTPUT);       // Set pin 13 as output
digitalWrite(13, HIGH);    // Turn pin 13 ON (5V)
digitalWrite(13, LOW);     // Turn pin 13 OFF (0V)

int buttonState = digitalRead(2);  // Read digital pin 2

// Analog pins
int sensorValue = analogRead(A0);  // Read analog pin A0 (0-1023)
analogWrite(9, 128);              // PWM output (0-255)</code></pre>
            </div>

            <div class="subsection">
                <h3>🔌 Component Programming Examples</h3>

                <h4>1. LED Blink</h4>
                <pre><code>void setup() {
  pinMode(13, OUTPUT);  // Set pin 13 as output
}

void loop() {
  digitalWrite(13, HIGH);  // Turn LED on
  delay(1000);            // Wait 1 second
  digitalWrite(13, LOW);   // Turn LED off
  delay(1000);            // Wait 1 second
}</code></pre>

                <h4>2. Button Input with Debouncing</h4>
                <pre><code>int buttonPin = 2;
int ledPin = 13;
int ledState = LOW;
int buttonState;
int lastButtonState = HIGH;
unsigned long lastDebounceTime = 0;
unsigned long debounceDelay = 50;

void setup() {
  pinMode(buttonPin, INPUT_PULLUP);
  pinMode(ledPin, OUTPUT);
  digitalWrite(ledPin, ledState);
}

void loop() {
  int reading = digitalRead(buttonPin);

  if (reading != lastButtonState) {
    lastDebounceTime = millis();
  }

  if ((millis() - lastDebounceTime) > debounceDelay) {
    if (reading != buttonState) {
      buttonState = reading;

      if (buttonState == LOW) {
        ledState = !ledState;
      }
    }
  }

  digitalWrite(ledPin, ledState);
  lastButtonState = reading;
}</code></pre>

                <h4>3. DHT Temperature Sensor</h4>
                <pre><code>#include &lt;DHT.h&gt;

#define DHTPIN 2
#define DHTTYPE DHT11

DHT dht(DHTPIN, DHTTYPE);

void setup() {
  Serial.begin(9600);
  dht.begin();
}

void loop() {
  float humidity = dht.readHumidity();
  float temperature = dht.readTemperature();

  if (isnan(humidity) || isnan(temperature)) {
    Serial.println("Failed to read from DHT sensor!");
    return;
  }

  Serial.print("Humidity: ");
  Serial.print(humidity);
  Serial.print("%  Temperature: ");
  Serial.print(temperature);
  Serial.println("°C");

  delay(2000);
}</code></pre>
                <h4>4. WiFi Connection (ESP32)</h4>
                <pre><code>#include &lt;WiFi.h&gt;

const char* ssid = "your_wifi_name";
const char* password = "your_wifi_password";

void setup() {
  Serial.begin(115200);

  WiFi.begin(ssid, password);

  while (WiFi.status() != WL_CONNECTED) {
    delay(1000);
    Serial.println("Connecting to WiFi...");
  }

  Serial.println("Connected to WiFi!");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());
}

void loop() {
  // Your main code here
}</code></pre>

                <h4>5. Bluetooth Communication (ESP32)</h4>
                <pre><code>#include "BluetoothSerial.h"

BluetoothSerial SerialBT;
int ledPin = 2;

void setup() {
  Serial.begin(115200);
  SerialBT.begin("ESP32test"); // Bluetooth device name
  Serial.println("The device started, now you can pair it with bluetooth!");
  pinMode(ledPin, OUTPUT);
}

void loop() {
  if (SerialBT.available()) {
    String message = SerialBT.readString();
    message.trim();

    Serial.println("Received: " + message);

    if (message == "ON") {
      digitalWrite(ledPin, HIGH);
      SerialBT.println("LED turned ON");
    }
    else if (message == "OFF") {
      digitalWrite(ledPin, LOW);
      SerialBT.println("LED turned OFF");
    }
  }

  delay(20);
}</code></pre>
            </div>

            <div class="subsection">
                <h3>🚗 Complete Project Example: Smart Home System</h3>
                <p>This example creates a complete smart home lighting system with WiFi control and automatic light sensing:</p>

                <pre><code>#include &lt;WiFi.h&gt;
#include &lt;WebServer.h&gt;

// WiFi credentials
const char* ssid = "your_wifi_name";
const char* password = "your_wifi_password";

// Pin definitions
const int ledPin = 2;
const int ldrPin = A0;
const int buttonPin = 4;

// Variables
WebServer server(80);
bool autoMode = true;
bool ledState = false;
int lightThreshold = 500;

void setup() {
  Serial.begin(115200);

  // Initialize pins
  pinMode(ledPin, OUTPUT);
  pinMode(buttonPin, INPUT_PULLUP);

  // Connect to WiFi
  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(1000);
    Serial.println("Connecting to WiFi...");
  }

  Serial.println("WiFi connected!");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());

  // Setup web server routes
  server.on("/", handleRoot);
  server.on("/toggle", handleToggle);
  server.on("/auto", handleAuto);
  server.on("/manual", handleManual);

  server.begin();
  Serial.println("HTTP server started");
}

void loop() {
  server.handleClient();

  // Check button press
  if (digitalRead(buttonPin) == LOW) {
    delay(50); // Debounce
    if (digitalRead(buttonPin) == LOW) {
      toggleLed();
      while (digitalRead(buttonPin) == LOW); // Wait for release
    }
  }

  // Auto mode logic
  if (autoMode) {
    int ldrValue = analogRead(ldrPin);
    if (ldrValue < lightThreshold && !ledState) {
      ledState = true;
      digitalWrite(ledPin, HIGH);
      Serial.println("Auto: LED ON (dark detected)");
    } else if (ldrValue >= lightThreshold && ledState) {
      ledState = false;
      digitalWrite(ledPin, LOW);
      Serial.println("Auto: LED OFF (light detected)");
    }
  }

  delay(100);
}

void handleRoot() {
  String html = "&lt;html&gt;&lt;head&gt;&lt;title&gt;Smart Lighting&lt;/title&gt;&lt;/head&gt;&lt;body&gt;";
  html += "&lt;h1&gt;Smart Home Lighting System&lt;/h1&gt;";
  html += "&lt;p&gt;LED Status: " + String(ledState ? "ON" : "OFF") + "&lt;/p&gt;";
  html += "&lt;p&gt;Mode: " + String(autoMode ? "AUTO" : "MANUAL") + "&lt;/p&gt;";
  html += "&lt;p&gt;Light Level: " + String(analogRead(ldrPin)) + "&lt;/p&gt;";
  html += "&lt;p&gt;&lt;a href=\"/toggle\"&gt;Toggle LED&lt;/a&gt;&lt;/p&gt;";
  html += "&lt;p&gt;&lt;a href=\"/auto\"&gt;Auto Mode&lt;/a&gt;&lt;/p&gt;";
  html += "&lt;p&gt;&lt;a href=\"/manual\"&gt;Manual Mode&lt;/a&gt;&lt;/p&gt;";
  html += "&lt;/body&gt;&lt;/html&gt;";

  server.send(200, "text/html", html);
}

void handleToggle() {
  toggleLed();
  server.send(200, "text/plain", "LED toggled");
}

void handleAuto() {
  autoMode = true;
  server.send(200, "text/plain", "Auto mode enabled");
}

void handleManual() {
  autoMode = false;
  server.send(200, "text/plain", "Manual mode enabled");
}

void toggleLed() {
  ledState = !ledState;
  digitalWrite(ledPin, ledState);
  Serial.println("LED " + String(ledState ? "ON" : "OFF"));
}</code></pre>
            </div>

            <div class="subsection">
                <h3>🔧 Troubleshooting</h3>

                <h4>Common Issues and Solutions</h4>

                <h5>1. Board Not Detected</h5>
                <p><strong>Problem:</strong> Arduino IDE doesn't recognize your board</p>
                <p><strong>Solutions:</strong></p>
                <ul>
                    <li>Check USB cable (try a different one)</li>
                    <li>Install board drivers</li>
                    <li>Try a different USB port</li>
                    <li>Restart Arduino IDE</li>
                    <li>Check if board is in bootloader mode (for ESP32/ESP8266)</li>
                </ul>

                <h5>2. Upload Failed</h5>
                <p><strong>Problem:</strong> Code won't upload to board</p>
                <p><strong>Solutions:</strong></p>
                <ul>
                    <li>Select correct board and port in Tools menu</li>
                    <li>Close Serial Monitor before uploading</li>
                    <li>Press and hold BOOT button during upload (ESP32/ESP8266)</li>
                    <li>Check for syntax errors in code</li>
                    <li>Reduce code size if memory is full</li>
                </ul>

                <h5>3. WiFi Connection Issues</h5>
                <p><strong>Problem:</strong> ESP32/ESP8266 won't connect to WiFi</p>
                <p><strong>Solutions:</strong></p>
                <ul>
                    <li>Double-check SSID and password</li>
                    <li>Ensure WiFi is 2.4GHz (not 5GHz)</li>
                    <li>Check signal strength</li>
                    <li>Try connecting to a mobile hotspot</li>
                    <li>Add delays in connection code</li>
                </ul>
            </div>

            <div class="subsection">
                <h3>📚 Best Practices</h3>

                <h4>Code Organization</h4>
                <pre><code>// Use meaningful variable names
int temperatureSensor = A0;  // Good
int a = A0;                  // Bad

// Add comments
// Read temperature every 5 seconds
if (millis() - lastReading > 5000) {
  temperature = readTemperature();
  lastReading = millis();
}

// Use constants for pin definitions
const int LED_PIN = 13;
const int BUTTON_PIN = 2;</code></pre>

                <h4>Error Handling</h4>
                <pre><code>// Always check sensor readings
float temp = dht.readTemperature();
if (isnan(temp)) {
  Serial.println("Error reading temperature!");
  return; // Exit function or handle error
}</code></pre>

                <h4>Additional Resources</h4>
                <ul>
                    <li><strong>Arduino Official Documentation:</strong> <a href="https://arduino.cc/reference" target="_blank">arduino.cc/reference</a></li>
                    <li><strong>ESP32 Documentation:</strong> <a href="https://docs.espressif.com" target="_blank">docs.espressif.com</a></li>
                    <li><strong>Arduino Forum:</strong> <a href="https://forum.arduino.cc" target="_blank">forum.arduino.cc</a></li>
                    <li><strong>GitHub Arduino Libraries:</strong> <a href="https://github.com/arduino-libraries" target="_blank">github.com/arduino-libraries</a></li>
                </ul>
            </div>

        </div>

        <div class="footer">
            <p>© 2024 SK Raihan - SKR Electronics Lab</p>
            <p>Arduverse v1.0.0 - Professional IoT Controller</p>
            <p>For support: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>YouTube: <a href="https://www.youtube.com/@skr_electronics_lab">SKR Electronics Lab</a></p>
        </div>
    </div>
</body>
</html>
